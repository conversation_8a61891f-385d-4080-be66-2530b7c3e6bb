/**
 * Simple Session-Based Authentication Middleware
 * For protecting dashboard routes
 */

// In-memory session store (in production, use Redis or database)
const activeSessions = new Map();

// Session configuration
const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours
const COOKIE_NAME = 'pressure_max_session';

/**
 * Generate a secure session ID
 */
function generateSessionId() {
    return 'sess_' + Math.random().toString(36).substring(2) + Date.now().toString(36);
}

/**
 * Create a new session
 */
function createSession(userId, userEmail) {
    const sessionId = generateSessionId();
    const session = {
        id: sessionId,
        userId: userId,
        userEmail: userEmail,
        createdAt: new Date(),
        lastAccessed: new Date(),
        expiresAt: new Date(Date.now() + SESSION_TIMEOUT)
    };
    
    activeSessions.set(sessionId, session);
    console.log(`✅ Session created for user ${userEmail}: ${sessionId}`);
    return session;
}

/**
 * Get session by ID
 */
function getSession(sessionId) {
    if (!sessionId) return null;
    
    const session = activeSessions.get(sessionId);
    if (!session) return null;
    
    // Check if session is expired
    if (new Date() > session.expiresAt) {
        activeSessions.delete(sessionId);
        console.log(`⏰ Session expired and removed: ${sessionId}`);
        return null;
    }
    
    // Update last accessed time
    session.lastAccessed = new Date();
    return session;
}

/**
 * Destroy a session
 */
function destroySession(sessionId) {
    if (sessionId && activeSessions.has(sessionId)) {
        activeSessions.delete(sessionId);
        console.log(`🗑️ Session destroyed: ${sessionId}`);
        return true;
    }
    return false;
}

/**
 * Clean up expired sessions
 */
function cleanupExpiredSessions() {
    const now = new Date();
    let cleanedCount = 0;
    
    for (const [sessionId, session] of activeSessions.entries()) {
        if (now > session.expiresAt) {
            activeSessions.delete(sessionId);
            cleanedCount++;
        }
    }
    
    if (cleanedCount > 0) {
        console.log(`🧹 Cleaned up ${cleanedCount} expired sessions`);
    }
}

/**
 * Middleware to require authentication
 */
function requireAuth(req, res, next) {
    // Get session ID from cookie
    const sessionId = req.cookies?.[COOKIE_NAME];
    
    if (!sessionId) {
        console.log('❌ No session cookie found, redirecting to login');
        return redirectToLogin(res);
    }
    
    // Validate session
    const session = getSession(sessionId);
    if (!session) {
        console.log('❌ Invalid or expired session, redirecting to login');
        // Clear invalid cookie
        res.clearCookie(COOKIE_NAME);
        return redirectToLogin(res);
    }
    
    // Attach session to request
    req.session = session;
    req.user = {
        id: session.userId,
        email: session.userEmail
    };
    
    console.log(`✅ Authenticated user: ${session.userEmail}`);
    next();
}

/**
 * Redirect to login page
 */
function redirectToLogin(res) {
    // Check if it's an API request
    if (res.req.path.startsWith('/api/')) {
        return res.status(401).json({
            error: 'Authentication required',
            code: 'AUTH_REQUIRED',
            redirectTo: 'http://localhost:3001/login'
        });
    }
    
    // For HTML pages, redirect to login
    res.redirect('http://localhost:3001/login');
}

/**
 * Login endpoint - create session
 */
function handleLogin(req, res) {
    const { email, password } = req.body;
    
    // Simple demo authentication (in production, verify against database)
    if (email && password) {
        // Create session
        const session = createSession('user_123', email);
        
        // Set session cookie
        res.cookie(COOKIE_NAME, session.id, {
            httpOnly: true,
            secure: false, // Set to true in production with HTTPS
            sameSite: 'lax',
            maxAge: SESSION_TIMEOUT
        });
        
        console.log(`🔐 User logged in: ${email}`);
        
        res.json({
            success: true,
            message: 'Login successful',
            user: {
                id: session.userId,
                email: session.userEmail
            },
            redirectTo: 'http://localhost:3000/'
        });
    } else {
        res.status(400).json({
            error: 'Email and password required',
            code: 'INVALID_CREDENTIALS'
        });
    }
}

/**
 * Logout endpoint - destroy session
 */
function handleLogout(req, res) {
    const sessionId = req.cookies?.[COOKIE_NAME];
    
    if (sessionId) {
        destroySession(sessionId);
    }
    
    // Clear session cookie
    res.clearCookie(COOKIE_NAME);
    
    console.log('🚪 User logged out');
    
    res.json({
        success: true,
        message: 'Logout successful',
        redirectTo: 'http://localhost:3001/login'
    });
}

/**
 * Get session info
 */
function getSessionInfo() {
    return {
        activeSessions: activeSessions.size,
        sessions: Array.from(activeSessions.values()).map(session => ({
            id: session.id,
            userEmail: session.userEmail,
            createdAt: session.createdAt,
            lastAccessed: session.lastAccessed,
            expiresAt: session.expiresAt
        }))
    };
}

// Clean up expired sessions every hour
setInterval(cleanupExpiredSessions, 60 * 60 * 1000);

module.exports = {
    requireAuth,
    handleLogin,
    handleLogout,
    createSession,
    getSession,
    destroySession,
    getSessionInfo,
    COOKIE_NAME
};
