/**
 * Shared Utilities
 * Common functions used across all pages
 */

// Notification system
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // Add styles if not already added
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: #1a1a1a;
                border: 1px solid #2a2a2a;
                border-radius: 8px;
                padding: 16px;
                color: #ffffff;
                z-index: 1000;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                animation: slideIn 0.3s ease;
            }
            
            .notification-info {
                border-left: 4px solid #3b82f6;
            }
            
            .notification-success {
                border-left: 4px solid #10b981;
            }
            
            .notification-warning {
                border-left: 4px solid #f59e0b;
            }
            
            .notification-error {
                border-left: 4px solid #ef4444;
            }
            
            .notification-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                gap: 12px;
            }
            
            .notification-close {
                background: none;
                border: none;
                color: #888888;
                cursor: pointer;
                font-size: 18px;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .notification-close:hover {
                color: #ffffff;
            }
            
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(styles);
    }
    
    // Add to page
    document.body.appendChild(notification);
    
    // Close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', function() {
        notification.remove();
    });
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Handle logout functionality
async function handleLogout() {
    try {
        // Show confirmation dialog
        const confirmed = confirm('Are you sure you want to logout?');
        if (!confirmed) {
            return;
        }

        // Show loading notification
        showNotification('Logging out...', 'info');

        // Call logout API endpoint
        const response = await fetch('/api/v1/auth/logout', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include' // Include cookies for session management
        });

        if (response.ok) {
            // Clear any local storage or session storage
            localStorage.clear();
            sessionStorage.clear();
            
            // Show success message
            showNotification('Logged out successfully!', 'success');
            
            // Redirect to login page after a short delay
            setTimeout(() => {
                window.location.href = 'http://localhost:3001/login';
            }, 1000);
        } else {
            throw new Error('Logout failed');
        }
    } catch (error) {
        console.error('Logout error:', error);
        showNotification('Error during logout. Please try again.', 'error');
        
        // Fallback: redirect to login anyway after error
        setTimeout(() => {
            window.location.href = 'http://localhost:3001/login';
        }, 2000);
    }
}

// Utility function to format numbers
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Load dashboard data from API
async function loadDashboardData() {
    try {
        // Check API health
        const healthResponse = await fetch('/health');
        const healthData = await healthResponse.json();
        
        if (healthData.status === 'healthy') {
            console.log('API is healthy:', healthData);
            updateApiStatus('healthy');
        }
        
        // Load Facebook data if available
        loadFacebookData();
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        updateApiStatus('error');
    }
}

// Load Facebook campaign data
async function loadFacebookData() {
    try {
        // Try to load ad accounts
        const accountsResponse = await fetch('/api/v1/facebook/ad-accounts');
        const accountsData = await accountsResponse.json();
        
        if (accountsData && accountsData.length > 0) {
            console.log('Facebook accounts loaded:', accountsData.length);
            updateFacebookStatus('connected');
        }
        
    } catch (error) {
        console.error('Error loading Facebook data:', error);
        updateFacebookStatus('disconnected');
    }
}

// Update API status indicator
function updateApiStatus(status) {
    // Add status indicator to header if not exists
    let statusIndicator = document.querySelector('.api-status');
    if (!statusIndicator) {
        statusIndicator = document.createElement('div');
        statusIndicator.className = 'api-status';
        const headerRight = document.querySelector('.header-right');
        if (headerRight) {
            headerRight.prepend(statusIndicator);
        }
    }
    
    statusIndicator.innerHTML = `
        <div class="status-dot status-${status}"></div>
        <span class="status-text">API ${status}</span>
    `;
    
    // Add styles if not already added
    if (!document.querySelector('#status-styles')) {
        const styles = document.createElement('style');
        styles.id = 'status-styles';
        styles.textContent = `
            .api-status {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 12px;
                color: #888888;
            }
            
            .status-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
            }
            
            .status-healthy {
                background: #10b981;
            }
            
            .status-error {
                background: #ef4444;
            }
            
            .status-warning {
                background: #f59e0b;
            }
        `;
        document.head.appendChild(styles);
    }
}

// Update Facebook connection status
function updateFacebookStatus(status) {
    console.log('Facebook status:', status);
    // Add Facebook-specific status updates here
}

// Handle window resize
window.addEventListener('resize', function() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar && window.innerWidth > 1024) {
        sidebar.classList.remove('open');
    }
});

// Smooth page navigation
function navigateToPage(url) {
    // Add a subtle fade effect during navigation
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.style.opacity = '0.8';
        mainContent.style.transition = 'opacity 0.1s ease';
    }

    // Navigate after a brief delay
    setTimeout(() => {
        window.location.href = url;
    }, 50);
}

// Enhance navigation links with smooth transitions
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth navigation to sidebar links
    setTimeout(() => {
        const navLinks = document.querySelectorAll('.nav-link[href^="/"], .nav-link[href^="http"]');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                const href = this.getAttribute('href');
                if (href && href !== '#' && !href.includes('target="_blank"')) {
                    e.preventDefault();
                    navigateToPage(href);
                }
            });
        });
    }, 500); // Wait for components to load
});
