// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
    
    // Initialize dashboard functionality
    initializeDashboard();
});

function initializeDashboard() {
    // Mobile menu toggle
    const menuToggle = document.querySelector('.menu-toggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('open');
        });
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 1024) {
                if (!sidebar.contains(e.target) && !menuToggle.contains(e.target)) {
                    sidebar.classList.remove('open');
                }
            }
        });
    }
    
    // Search functionality
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            // Add search logic here
            console.log('Searching for:', searchTerm);
        });
    }
    
    // Navigation active state
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Add active class to clicked item
            this.closest('.nav-item').classList.add('active');
        });
    });
    
    // Task card interactions
    const taskCards = document.querySelectorAll('.task-card');
    taskCards.forEach(card => {
        card.addEventListener('click', function() {
            // Add task card click logic here
            console.log('Task card clicked:', this.querySelector('.task-title').textContent);
        });
    });
    
    // New project button
    const newProjectBtn = document.querySelector('.new-project-btn');
    if (newProjectBtn) {
        newProjectBtn.addEventListener('click', function() {
            // Add new project logic here
            console.log('New project button clicked');
            showNotification('New project feature coming soon!', 'info');
        });
    }
    
    // Header buttons
    const headerBtns = document.querySelectorAll('.header-btn');
    headerBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const icon = this.querySelector('i').getAttribute('data-lucide');
            console.log('Header button clicked:', icon);
            
            if (icon === 'bell') {
                showNotification('No new notifications', 'info');
            } else if (icon === 'settings') {
                showNotification('Settings panel coming soon!', 'info');
            }
        });
    });
    
    // Dashboard action buttons
    const dashboardBtns = document.querySelectorAll('.dashboard-actions .btn');
    dashboardBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const text = this.textContent.trim();
            console.log('Dashboard button clicked:', text);
            
            if (text.includes('Export')) {
                showNotification('Export functionality coming soon!', 'info');
            } else if (text.includes('Create Campaign')) {
                showNotification('Redirecting to campaign creation...', 'success');
                // Could redirect to campaign creation page
            }
        });
    });
    
    // Board control buttons
    const boardBtns = document.querySelectorAll('.board-controls .btn');
    boardBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const text = this.textContent.trim();
            console.log('Board button clicked:', text);
            
            if (text.includes('Filter')) {
                showNotification('Filter options coming soon!', 'info');
            } else if (text.includes('Date Range')) {
                showNotification('Date range picker coming soon!', 'info');
            }
        });
    });
    
    // Load real data
    loadDashboardData();
}

// Notification system
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // Add styles if not already added
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: #1a1a1a;
                border: 1px solid #2a2a2a;
                border-radius: 8px;
                padding: 16px;
                color: #ffffff;
                z-index: 1000;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                animation: slideIn 0.3s ease;
            }
            
            .notification-info {
                border-left: 4px solid #3b82f6;
            }
            
            .notification-success {
                border-left: 4px solid #10b981;
            }
            
            .notification-warning {
                border-left: 4px solid #f59e0b;
            }
            
            .notification-error {
                border-left: 4px solid #ef4444;
            }
            
            .notification-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                gap: 12px;
            }
            
            .notification-close {
                background: none;
                border: none;
                color: #888888;
                cursor: pointer;
                font-size: 18px;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .notification-close:hover {
                color: #ffffff;
            }
            
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(styles);
    }
    
    // Add to page
    document.body.appendChild(notification);
    
    // Close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', function() {
        notification.remove();
    });
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Load dashboard data from API
async function loadDashboardData() {
    try {
        // Check API health
        const healthResponse = await fetch('/health');
        const healthData = await healthResponse.json();
        
        if (healthData.status === 'healthy') {
            console.log('API is healthy:', healthData);
            updateApiStatus('healthy');
        }
        
        // Load Facebook data if available
        loadFacebookData();
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        updateApiStatus('error');
    }
}

// Load Facebook campaign data
async function loadFacebookData() {
    try {
        // Try to load ad accounts
        const accountsResponse = await fetch('/api/v1/facebook/ad-accounts');
        const accountsData = await accountsResponse.json();
        
        if (accountsData && accountsData.length > 0) {
            console.log('Facebook accounts loaded:', accountsData.length);
            updateFacebookStatus('connected');
        }
        
    } catch (error) {
        console.error('Error loading Facebook data:', error);
        updateFacebookStatus('disconnected');
    }
}

// Update API status indicator
function updateApiStatus(status) {
    // Add status indicator to header if not exists
    let statusIndicator = document.querySelector('.api-status');
    if (!statusIndicator) {
        statusIndicator = document.createElement('div');
        statusIndicator.className = 'api-status';
        document.querySelector('.header-right').prepend(statusIndicator);
    }
    
    statusIndicator.innerHTML = `
        <div class="status-dot status-${status}"></div>
        <span class="status-text">API ${status}</span>
    `;
    
    // Add styles if not already added
    if (!document.querySelector('#status-styles')) {
        const styles = document.createElement('style');
        styles.id = 'status-styles';
        styles.textContent = `
            .api-status {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 12px;
                color: #888888;
            }
            
            .status-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
            }
            
            .status-healthy {
                background: #10b981;
            }
            
            .status-error {
                background: #ef4444;
            }
            
            .status-warning {
                background: #f59e0b;
            }
        `;
        document.head.appendChild(styles);
    }
}

// Update Facebook connection status
function updateFacebookStatus(status) {
    console.log('Facebook status:', status);
    // Add Facebook-specific status updates here
}

// Utility function to format numbers
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Handle window resize
window.addEventListener('resize', function() {
    const sidebar = document.querySelector('.sidebar');
    if (window.innerWidth > 1024) {
        sidebar.classList.remove('open');
    }
});

// Handle logout functionality
async function handleLogout() {
    try {
        // Show confirmation dialog
        const confirmed = confirm('Are you sure you want to logout?');
        if (!confirmed) {
            return;
        }

        // Show loading notification
        showNotification('Logging out...', 'info');

        // Call logout API endpoint
        const response = await fetch('/api/v1/auth/logout', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include' // Include cookies for session management
        });

        if (response.ok) {
            // Clear any local storage or session storage
            localStorage.clear();
            sessionStorage.clear();

            // Show success message
            showNotification('Logged out successfully!', 'success');

            // Redirect to login page after a short delay
            setTimeout(() => {
                window.location.href = 'http://localhost:3001/login';
            }, 1000);
        } else {
            throw new Error('Logout failed');
        }
    } catch (error) {
        console.error('Logout error:', error);
        showNotification('Error during logout. Please try again.', 'error');

        // Fallback: redirect to login anyway after error
        setTimeout(() => {
            window.location.href = 'http://localhost:3001/login';
        }, 2000);
    }
}
