[{"C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js": "3", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js": "4", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js": "5", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js": "6", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js": "7", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js": "8", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js": "9", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js": "10", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js": "11", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js": "12", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js": "13", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js": "14", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Dashboard.js": "15", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Landing.js": "16", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\lib\\supabase.js": "17", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Login.js": "18", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Signup.js": "19", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Testing.js": "20"}, {"size": 232, "mtime": 1750870627179, "results": "21", "hashOfConfig": "22"}, {"size": 1835, "mtime": 1750998212651, "results": "23", "hashOfConfig": "22"}, {"size": 11019, "mtime": 1750998922321, "results": "24", "hashOfConfig": "22"}, {"size": 18514, "mtime": 1750938756494, "results": "25", "hashOfConfig": "22"}, {"size": 7324, "mtime": 1750904956983, "results": "26", "hashOfConfig": "22"}, {"size": 8243, "mtime": 1750873838649, "results": "27", "hashOfConfig": "22"}, {"size": 4969, "mtime": 1751001959609, "results": "28", "hashOfConfig": "22"}, {"size": 4416, "mtime": 1750938756489, "results": "29", "hashOfConfig": "22"}, {"size": 4531, "mtime": 1750906642416, "results": "30", "hashOfConfig": "22"}, {"size": 4881, "mtime": 1750906513515, "results": "31", "hashOfConfig": "22"}, {"size": 15521, "mtime": 1750913282515, "results": "32", "hashOfConfig": "22"}, {"size": 11191, "mtime": 1750915070114, "results": "33", "hashOfConfig": "22"}, {"size": 28034, "mtime": 1750938756493, "results": "34", "hashOfConfig": "22"}, {"size": 21576, "mtime": 1750938756324, "results": "35", "hashOfConfig": "22"}, {"size": 6039, "mtime": 1750999559481, "results": "36", "hashOfConfig": "22"}, {"size": 32308, "mtime": 1750992242990, "results": "37", "hashOfConfig": "22"}, {"size": 526, "mtime": 1750992184196, "results": "38", "hashOfConfig": "22"}, {"size": 8234, "mtime": 1751000268112, "results": "39", "hashOfConfig": "22"}, {"size": 14523, "mtime": 1750994562197, "results": "40", "hashOfConfig": "22"}, {"size": 3048, "mtime": 1750998108215, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1klh60u", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js", ["102", "103"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js", ["104", "105"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js", ["106", "107"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js", ["108"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js", ["109"], ["110", "111"], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js", [], ["112", "113"], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Landing.js", ["114", "115", "116", "117", "118", "119", "120", "121", "122", "123"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\lib\\supabase.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Login.js", ["124"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Signup.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Testing.js", [], [], {"ruleId": "125", "severity": 1, "message": "126", "line": 65, "column": 6, "nodeType": "127", "endLine": 65, "endColumn": 23, "suggestions": "128"}, {"ruleId": "125", "severity": 1, "message": "126", "line": 76, "column": 6, "nodeType": "127", "endLine": 76, "endColumn": 46, "suggestions": "129"}, {"ruleId": "130", "severity": 1, "message": "131", "line": 8, "column": 28, "nodeType": "132", "messageId": "133", "endLine": 8, "endColumn": 32}, {"ruleId": "125", "severity": 1, "message": "134", "line": 22, "column": 6, "nodeType": "127", "endLine": 22, "endColumn": 23, "suggestions": "135"}, {"ruleId": "130", "severity": 1, "message": "136", "line": 5, "column": 20, "nodeType": "132", "messageId": "133", "endLine": 5, "endColumn": 25}, {"ruleId": "125", "severity": 1, "message": "137", "line": 25, "column": 6, "nodeType": "127", "endLine": 25, "endColumn": 32, "suggestions": "138"}, {"ruleId": "125", "severity": 1, "message": "139", "line": 24, "column": 6, "nodeType": "127", "endLine": 24, "endColumn": 8, "suggestions": "140"}, {"ruleId": "130", "severity": 1, "message": "141", "line": 23, "column": 10, "nodeType": "132", "messageId": "133", "endLine": 23, "endColumn": 27}, {"ruleId": "125", "severity": 1, "message": "142", "line": 87, "column": 6, "nodeType": "127", "endLine": 87, "endColumn": 8, "suggestions": "143", "suppressions": "144"}, {"ruleId": "125", "severity": 1, "message": "145", "line": 94, "column": 6, "nodeType": "127", "endLine": 94, "endColumn": 23, "suggestions": "146", "suppressions": "147"}, {"ruleId": "125", "severity": 1, "message": "137", "line": 108, "column": 6, "nodeType": "127", "endLine": 108, "endColumn": 8, "suggestions": "148", "suppressions": "149"}, {"ruleId": "125", "severity": 1, "message": "145", "line": 115, "column": 6, "nodeType": "127", "endLine": 115, "endColumn": 23, "suggestions": "150", "suppressions": "151"}, {"ruleId": "130", "severity": 1, "message": "152", "line": 7, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 7, "endColumn": 11}, {"ruleId": "153", "severity": 1, "message": "154", "line": 525, "column": 19, "nodeType": "155", "endLine": 529, "endColumn": 25}, {"ruleId": "153", "severity": 1, "message": "154", "line": 632, "column": 23, "nodeType": "155", "endLine": 632, "endColumn": 59}, {"ruleId": "153", "severity": 1, "message": "154", "line": 633, "column": 23, "nodeType": "155", "endLine": 633, "endColumn": 59}, {"ruleId": "153", "severity": 1, "message": "154", "line": 640, "column": 23, "nodeType": "155", "endLine": 640, "endColumn": 59}, {"ruleId": "153", "severity": 1, "message": "154", "line": 641, "column": 23, "nodeType": "155", "endLine": 641, "endColumn": 59}, {"ruleId": "153", "severity": 1, "message": "154", "line": 642, "column": 23, "nodeType": "155", "endLine": 642, "endColumn": 59}, {"ruleId": "153", "severity": 1, "message": "154", "line": 643, "column": 23, "nodeType": "155", "endLine": 643, "endColumn": 59}, {"ruleId": "153", "severity": 1, "message": "154", "line": 671, "column": 17, "nodeType": "155", "endLine": 671, "endColumn": 53}, {"ruleId": "153", "severity": 1, "message": "154", "line": 672, "column": 17, "nodeType": "155", "endLine": 672, "endColumn": 53}, {"ruleId": "130", "severity": 1, "message": "156", "line": 14, "column": 9, "nodeType": "132", "messageId": "133", "endLine": 14, "endColumn": 17}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array.", "ArrayExpression", ["157"], ["158"], "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has missing dependencies: 'checkServerHealth' and 'loadUserProfile'. Either include them or remove the dependency array.", ["159"], "'Users' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAdAccounts'. Either include it or remove the dependency array.", ["160"], "React Hook useEffect has missing dependencies: 'checkFacebookAuth' and 'handleOAuthCallback'. Either include them or remove the dependency array.", ["161"], "'leadFormTemplates' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFormData'. Either include it or remove the dependency array.", ["162"], ["163"], "React Hook useEffect has a missing dependency: 'loadLeadForms'. Either include it or remove the dependency array.", ["164"], ["165"], ["166"], ["167"], ["168"], ["169"], "'Sparkles' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'navigate' is assigned a value but never used.", {"desc": "170", "fix": "171"}, {"desc": "172", "fix": "173"}, {"desc": "174", "fix": "175"}, {"desc": "176", "fix": "177"}, {"desc": "178", "fix": "179"}, {"desc": "180", "fix": "181"}, {"kind": "182", "justification": "183"}, {"desc": "184", "fix": "185"}, {"kind": "182", "justification": "183"}, {"desc": "186", "fix": "187"}, {"kind": "182", "justification": "183"}, {"desc": "184", "fix": "188"}, {"kind": "182", "justification": "183"}, "Update the dependencies array to be: [loadCampaigns, selectedAccount]", {"range": "189", "text": "190"}, "Update the dependencies array to be: [activeTab, selectedAccount, loadedData, loadCampaigns]", {"range": "191", "text": "192"}, "Update the dependencies array to be: [checkServerHealth, isAuthenticated, loadUserProfile]", {"range": "193", "text": "194"}, "Update the dependencies array to be: [isConnected, accessToken, loadAdAccounts]", {"range": "195", "text": "196"}, "Update the dependencies array to be: [checkFacebookAuth, handleOAuthCallback]", {"range": "197", "text": "198"}, "Update the dependencies array to be: [loadFormData]", {"range": "199", "text": "200"}, "directive", "", "Update the dependencies array to be: [loadLeadForms, selectedAccount]", {"range": "201", "text": "202"}, "Update the dependencies array to be: [loadAdAccounts]", {"range": "203", "text": "204"}, {"range": "205", "text": "202"}, [2408, 2425], "[load<PERSON><PERSON>ai<PERSON><PERSON>, selectedAccount]", [2833, 2873], "[activeTab, selectedAccount, loadedData, loadCampaigns]", [722, 739], "[checkServerHealth, isAuthenticated, loadUserProfile]", [724, 750], "[isConnected, accessToken, loadAdAccounts]", [762, 764], "[checkFacebookAuth, handleOAuthCallback]", [2300, 2302], "[loadFormData]", [2447, 2464], "[loadLeadForms, selectedAccount]", [2972, 2974], "[loadAdAccounts]", [3119, 3136]]