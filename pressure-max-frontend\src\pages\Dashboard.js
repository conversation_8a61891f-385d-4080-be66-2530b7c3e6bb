import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useFacebook } from '../contexts/FacebookContext';
import { useNavigate } from 'react-router-dom';
import { LogOut, User, Home, Settings, TrendingUp, Users, DollarSign, Eye, TestTube } from 'lucide-react';

const Dashboard = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const { isConnected: facebookConnected } = useFacebook();
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalCampaigns: 0,
    activeAds: 0,
    totalSpend: 0,
    totalLeads: 0
  });

  const handleLogout = async () => {
    await logout();
    navigate('/landing');
  };

  const handleGoToLanding = () => {
    navigate('/landing');
  };

  const handleGoToTesting = () => {
    navigate('/testing');
  };

  useEffect(() => {
    // Load dashboard stats
    // This would typically fetch from your API
    setStats({
      totalCampaigns: 12,
      activeAds: 8,
      totalSpend: 2450.75,
      totalLeads: 156
    });
  }, []);

  if (!isAuthenticated) {
    navigate('/landing');
    return null;
  }

  return (
    <div className="dashboard">
      {/* Dashboard Header */}
      <header className="dashboard-header">
        <div className="dashboard-header-content">
          <div className="dashboard-title">
            <h1>📊 Pressure Max Dashboard</h1>
            <p>Monitor your Facebook marketing campaigns and performance</p>
          </div>

          <div className="dashboard-user-section">
            {user && (
              <div className="user-info">
                <User size={16} />
                <span>Welcome, {user.firstName || user.email}</span>
              </div>
            )}

            <div className="dashboard-actions">
              <button onClick={handleGoToTesting} className="nav-btn">
                <TestTube size={16} />
                API Testing
              </button>
              <button onClick={handleGoToLanding} className="nav-btn">
                <Home size={16} />
                Landing
              </button>
              <button onClick={handleLogout} className="logout-btn">
                <LogOut size={16} />
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Dashboard Main Content */}
      <main className="dashboard-main">
        {/* Connection Status */}
        <div className="connection-status">
          <div className={`status-card ${facebookConnected ? 'connected' : 'disconnected'}`}>
            <div className="status-icon">
              {facebookConnected ? '✅' : '❌'}
            </div>
            <div className="status-info">
              <h3>Facebook Connection</h3>
              <p>{facebookConnected ? 'Connected and ready' : 'Not connected'}</p>
            </div>
            {!facebookConnected && (
              <button onClick={handleGoToTesting} className="btn btn-primary">
                Connect Facebook
              </button>
            )}
          </div>
        </div>

        {/* Stats Grid */}
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">
              <TrendingUp size={24} />
            </div>
            <div className="stat-content">
              <h3>Total Campaigns</h3>
              <p className="stat-number">{stats.totalCampaigns}</p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">
              <Eye size={24} />
            </div>
            <div className="stat-content">
              <h3>Active Ads</h3>
              <p className="stat-number">{stats.activeAds}</p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">
              <DollarSign size={24} />
            </div>
            <div className="stat-content">
              <h3>Total Spend</h3>
              <p className="stat-number">${stats.totalSpend.toFixed(2)}</p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">
              <Users size={24} />
            </div>
            <div className="stat-content">
              <h3>Total Leads</h3>
              <p className="stat-number">{stats.totalLeads}</p>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="quick-actions">
          <h2>Quick Actions</h2>
          <div className="actions-grid">
            <button onClick={handleGoToTesting} className="action-card">
              <TestTube size={32} />
              <h3>API Testing</h3>
              <p>Test and debug API endpoints</p>
            </button>

            <button onClick={handleGoToTesting} className="action-card">
              <TrendingUp size={32} />
              <h3>Create Campaign</h3>
              <p>Launch a new Facebook campaign</p>
            </button>

            <button onClick={handleGoToTesting} className="action-card">
              <Users size={32} />
              <h3>Manage Lead Forms</h3>
              <p>Create and edit lead forms</p>
            </button>

            <button onClick={handleGoToTesting} className="action-card">
              <Settings size={32} />
              <h3>Account Settings</h3>
              <p>Manage your account preferences</p>
            </button>
          </div>
        </div>
      </main>

      {/* Dashboard Footer */}
      <footer className="dashboard-footer">
        <p>Pressure Max Dashboard - Facebook Marketing Management</p>
        <p>Connected to API: <code>http://localhost:3000</code></p>
      </footer>
    </div>
  );
};

export default Dashboard;
