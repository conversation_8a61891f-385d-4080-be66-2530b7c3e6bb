import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Eye, EyeOff, Mail, Lock, ArrowRight, Zap } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { signIn } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      console.log('Attempting to sign in...');
      await signIn(email, password);
      console.log('Sign in successful, navigating to dashboard...');
      navigate('/dashboard');
    } catch (error) {
      console.error('Sign in error:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black flex items-center justify-center relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 circuit-bg opacity-20"></div>
      <div className="particles">
        <div className="particle" style={{left: '10%', animationDelay: '0s'}}></div>
        <div className="particle" style={{left: '30%', animationDelay: '2s'}}></div>
        <div className="particle" style={{left: '50%', animationDelay: '4s'}}></div>
        <div className="particle" style={{left: '70%', animationDelay: '1s'}}></div>
        <div className="particle" style={{left: '90%', animationDelay: '3s'}}></div>
      </div>

      <div style={{
        maxWidth: '500px',
        width: '100%',
        margin: '0 auto',
        padding: '0 1.5rem',
        position: 'relative',
        zIndex: 10,
        boxSizing: 'border-box'
      }}>
        {/* Logo */}
        <div className="text-center mb-8">
          <Link to="/" className="inline-flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-lg flex items-center justify-center animate-glow">
              <Zap className="text-black" size={28} />
            </div>
            <span className="text-3xl font-bold font-orbitron tracking-wider text-cyan-400">
              PressureMax
            </span>
          </Link>
        </div>

        {/* Login Form */}
        <div className="glass-strong p-8 rounded-2xl border border-cyan-500/30 animate-fade-in-up" style={{width: '100%', maxWidth: '100%', boxSizing: 'border-box'}}>
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold font-orbitron text-white mb-2">
              Welcome Back
            </h1>
            <p className="text-gray-400">
              Sign in to access your marketing dashboard
            </p>
          </div>

          {error && (
            <div className="bg-red-500/10 border border-red-500/30 text-red-400 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="auth-form" style={{
            width: '100%',
            maxWidth: '100%',
            boxSizing: 'border-box',
            display: 'flex',
            flexDirection: 'column',
            gap: '1.5rem'
          }}>
            {/* Email Field */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Email Address
              </label>
              <div className="relative flex items-center">
                <Mail className="absolute text-gray-500 pointer-events-none" size={18} style={{ left: '16px', zIndex: 10 }} />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full bg-white border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 transition-colors"
                  style={{
                    paddingLeft: '44px',
                    paddingRight: '16px',
                    paddingTop: '16px',
                    paddingBottom: '16px',
                    fontSize: '16px',
                    lineHeight: '24px',
                    height: '56px'
                  }}
                  placeholder="Enter your email"
                  required
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Password
              </label>
              <div className="relative flex items-center">
                <Lock className="absolute text-gray-500 pointer-events-none" size={18} style={{ left: '16px', zIndex: 10 }} />
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full bg-white border border-gray-300 rounded-lg text-black placeholder-gray-500 focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 transition-colors"
                  style={{
                    paddingLeft: '44px',
                    paddingRight: '44px',
                    paddingTop: '16px',
                    paddingBottom: '16px',
                    fontSize: '16px',
                    lineHeight: '24px',
                    height: '56px'
                  }}
                  placeholder="Enter your password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute text-gray-600 hover:text-cyan-500 transition-colors"
                  style={{
                    right: '16px',
                    zIndex: 10,
                    width: '18px',
                    height: '18px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
            </div>

            {/* Forgot Password */}
            <div className="text-right">
              <Link 
                to="/forgot-password" 
                className="text-sm text-cyan-400 hover:text-cyan-300 transition-colors"
              >
                Forgot your password?
              </Link>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full cta-button btn-3d py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                fontSize: '18px',
                padding: '1rem 2rem',
                minHeight: '56px'
              }}
            >
              {loading ? (
                <span>Signing In...</span>
              ) : (
                <>
                  <span>Sign In</span>
                  <ArrowRight size={20} />
                </>
              )}
            </button>
          </form>

          {/* Sign Up Link */}
          <div className="mt-8 text-center">
            <p className="text-gray-400">
              Don't have an account?{' '}
              <Link 
                to="/signup" 
                className="text-cyan-400 hover:text-cyan-300 font-semibold transition-colors"
              >
                Sign up for free
              </Link>
            </p>
          </div>

          {/* Back to Home */}
          <div className="mt-6 text-center">
            <Link 
              to="/" 
              className="text-sm text-gray-500 hover:text-gray-400 transition-colors"
            >
              ← Back to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Login;
