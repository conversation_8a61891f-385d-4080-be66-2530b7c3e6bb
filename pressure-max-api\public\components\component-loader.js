/**
 * Component Loader System
 * Dynamically loads and injects reusable components into pages
 */

class ComponentLoader {
    constructor() {
        this.components = new Map();
        this.loadedComponents = new Set();
    }

    /**
     * Load a component from file and cache it
     */
    async loadComponent(componentName) {
        if (this.components.has(componentName)) {
            return this.components.get(componentName);
        }

        try {
            const response = await fetch(`/components/${componentName}.html`);
            if (!response.ok) {
                throw new Error(`Failed to load component: ${componentName}`);
            }
            
            const html = await response.text();
            this.components.set(componentName, html);
            return html;
        } catch (error) {
            console.error(`Error loading component ${componentName}:`, error);
            return null;
        }
    }

    /**
     * Inject a component into a target element
     */
    async injectComponent(componentName, targetSelector) {
        const target = document.querySelector(targetSelector);
        if (!target) {
            console.error(`Target element not found: ${targetSelector}`);
            return false;
        }

        // Add loading state
        target.classList.add('loading');

        const componentHtml = await this.loadComponent(componentName);
        if (!componentHtml) {
            console.error(`Failed to load component: ${componentName}`);
            target.classList.remove('loading');
            return false;
        }

        // Inject component and remove loading state
        target.innerHTML = componentHtml;
        target.classList.remove('loading');
        this.loadedComponents.add(componentName);

        // Re-initialize Lucide icons for the new component
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        return true;
    }

    /**
     * Load multiple components
     */
    async loadComponents(components) {
        const promises = components.map(({ name, target }) => 
            this.injectComponent(name, target)
        );
        
        const results = await Promise.all(promises);
        return results.every(result => result === true);
    }

    /**
     * Set active navigation item based on current page
     */
    setActiveNavItem(pageId) {
        // Remove active class from all nav items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to current page nav item
        const currentNavItem = document.querySelector(`[data-page="${pageId}"]`);
        if (currentNavItem) {
            currentNavItem.classList.add('active');
        }
    }

    /**
     * Initialize component system for a page
     */
    async initializePage(pageId, customComponents = []) {
        // Default components that every page should have
        const defaultComponents = [
            { name: 'sidebar', target: '#sidebar-container' },
            { name: 'header', target: '#header-container' }
        ];

        // Combine default and custom components
        const allComponents = [...defaultComponents, ...customComponents];

        // Show loading states immediately
        allComponents.forEach(({ target }) => {
            const element = document.querySelector(target);
            if (element) {
                element.classList.add('loading');
            }
        });

        // Load all components in parallel for faster loading
        const success = await this.loadComponents(allComponents);

        if (success) {
            // Set active navigation
            this.setActiveNavItem(pageId);

            // Initialize component functionality
            this.initializeComponentFunctionality();

            // Smooth fade-in effect
            setTimeout(() => {
                allComponents.forEach(({ target }) => {
                    const element = document.querySelector(target);
                    if (element) {
                        element.style.opacity = '1';
                    }
                });
            }, 50);

            console.log(`Page ${pageId} initialized successfully`);
        } else {
            console.error(`Failed to initialize page ${pageId}`);
            // Remove loading states on failure
            allComponents.forEach(({ target }) => {
                const element = document.querySelector(target);
                if (element) {
                    element.classList.remove('loading');
                }
            });
        }

        return success;
    }

    /**
     * Initialize functionality for loaded components
     */
    initializeComponentFunctionality() {
        // Mobile menu toggle
        const menuToggle = document.querySelector('.menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        if (menuToggle && sidebar) {
            menuToggle.addEventListener('click', function() {
                sidebar.classList.toggle('open');
            });
            
            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 1024) {
                    if (!sidebar.contains(e.target) && !menuToggle.contains(e.target)) {
                        sidebar.classList.remove('open');
                    }
                }
            });
        }

        // Search functionality
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', function(e) {
                const searchTerm = e.target.value.toLowerCase();
                console.log('Searching for:', searchTerm);
                // Add search logic here
            });
        }

        // Navigation active state
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // Remove active class from all nav items
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                // Add active class to clicked item
                this.closest('.nav-item').classList.add('active');
            });
        });

        // Header buttons
        const headerBtns = document.querySelectorAll('.header-btn:not(.logout-btn)');
        headerBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const icon = this.querySelector('i').getAttribute('data-lucide');
                console.log('Header button clicked:', icon);
                
                if (icon === 'bell') {
                    showNotification('No new notifications', 'info');
                } else if (icon === 'settings') {
                    showNotification('Settings panel coming soon!', 'info');
                }
            });
        });

        // New project button
        const newProjectBtn = document.querySelector('.new-project-btn');
        if (newProjectBtn) {
            newProjectBtn.addEventListener('click', function() {
                console.log('New project button clicked');
                showNotification('New project feature coming soon!', 'info');
            });
        }
    }
}

// Create global instance
window.componentLoader = new ComponentLoader();

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const pageId = document.body.getAttribute('data-page') || 'unknown';

    if (pageId !== 'unknown') {
        // Initialize immediately for faster loading
        window.componentLoader.initializePage(pageId).then(() => {
            console.log('Components loaded successfully for page:', pageId);
        }).catch((error) => {
            console.error('Failed to load components for page:', pageId, error);
        });
    }
});

// Also try to preload components on page ready for even faster subsequent loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', preloadComponents);
} else {
    preloadComponents();
}

function preloadComponents() {
    // Preload common components in the background
    if (window.componentLoader) {
        window.componentLoader.loadComponent('sidebar');
        window.componentLoader.loadComponent('header');
    }
}
