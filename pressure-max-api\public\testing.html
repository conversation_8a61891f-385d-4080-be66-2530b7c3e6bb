<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pressure Max API Testing Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #2563eb;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .nav-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .nav-link {
            padding: 10px 20px;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: background 0.2s ease;
        }

        .nav-link:hover {
            background: #2563eb;
        }

        .nav-link.secondary {
            background: #6b7280;
        }

        .nav-link.secondary:hover {
            background: #4b5563;
        }

        .section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .section h2 {
            color: #1f2937;
            font-size: 1.8rem;
            margin-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }

        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .api-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: border-color 0.2s ease;
        }

        .api-card:hover {
            border-color: #3b82f6;
        }

        .api-card h3 {
            color: #1f2937;
            margin-bottom: 10px;
        }

        .api-card p {
            color: #6b7280;
            margin-bottom: 15px;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-healthy {
            background: #10b981;
        }

        .status-error {
            background: #ef4444;
        }

        .status-warning {
            background: #f59e0b;
        }

        .feature-list {
            list-style: none;
            margin-top: 20px;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .feature-list li:before {
            content: "✅";
            margin-right: 10px;
        }

        .code-block {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            margin: 15px 0;
            overflow-x: auto;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .alert-info {
            background: #dbeafe;
            border: 1px solid #93c5fd;
            color: #1e40af;
        }

        .alert-success {
            background: #d1fae5;
            border: 1px solid #6ee7b7;
            color: #065f46;
        }

        .alert-warning {
            background: #fef3c7;
            border: 1px solid #fcd34d;
            color: #92400e;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .nav-links {
                flex-direction: column;
                align-items: center;
            }
            
            .api-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🚀 Pressure Max API Testing Interface</h1>
            <p>Comprehensive testing environment for the Pressure Max API backend</p>
            
            <div class="nav-links">
                <a href="/" class="nav-link secondary">← Back to Dashboard</a>
                <a href="http://localhost:3001" class="nav-link" target="_blank">React Frontend</a>
                <a href="/health" class="nav-link" target="_blank">API Health</a>
                <button onclick="handleLogout()" class="nav-link" style="background: #dc2626; color: white; border: none; cursor: pointer; border-radius: 6px;">🚪 Logout</button>
            </div>
        </header>

        <section class="section">
            <h2>🔧 API Status & Health</h2>
            <div class="api-grid">
                <div class="api-card">
                    <h3><span class="status-indicator status-healthy"></span>Server Health</h3>
                    <p>Monitor the current status of the Pressure Max API server</p>
                    <button class="btn btn-primary" onclick="checkHealth()">Check Health</button>
                    <div id="health-result" class="code-block" style="display: none;"></div>
                </div>
                
                <div class="api-card">
                    <h3><span class="status-indicator status-warning"></span>Cache Management</h3>
                    <p>Clear the API cache to force fresh data retrieval</p>
                    <button class="btn btn-secondary" onclick="clearCache()">Clear Cache</button>
                    <div id="cache-result" class="code-block" style="display: none;"></div>
                </div>
            </div>
        </section>

        <section class="section">
            <h2>📱 Facebook Marketing API</h2>
            <div class="alert alert-info">
                <strong>Note:</strong> Facebook API integration with rate limiting and caching for production use.
            </div>
            
            <div class="api-grid">
                <div class="api-card">
                    <h3>🔐 OAuth Authentication</h3>
                    <p>Generate Facebook OAuth URL for user authentication</p>
                    <button class="btn btn-primary" onclick="getFacebookOAuth()">Get OAuth URL</button>
                    <div id="oauth-result" class="code-block" style="display: none;"></div>
                </div>
                
                <div class="api-card">
                    <h3>🏢 Ad Accounts</h3>
                    <p>Retrieve all Facebook ad accounts with caching</p>
                    <button class="btn btn-primary" onclick="getAdAccounts()">Load Ad Accounts</button>
                    <div id="accounts-result" class="code-block" style="display: none;"></div>
                </div>
                
                <div class="api-card">
                    <h3>📊 Campaigns</h3>
                    <p>Get campaigns with nested ad sets and ads data</p>
                    <button class="btn btn-primary" onclick="getCampaigns()">Load Campaigns</button>
                    <div id="campaigns-result" class="code-block" style="display: none;"></div>
                </div>
                
                <div class="api-card">
                    <h3>📋 Lead Forms</h3>
                    <p>Retrieve Facebook lead generation forms</p>
                    <button class="btn btn-primary" onclick="getLeadForms()">Load Lead Forms</button>
                    <div id="leadforms-result" class="code-block" style="display: none;"></div>
                </div>
            </div>
        </section>

        <section class="section">
            <h2>🔑 Authentication System</h2>
            <div class="api-grid">
                <div class="api-card">
                    <h3>👤 User Registration</h3>
                    <p>Test user registration endpoint</p>
                    <button class="btn btn-primary" onclick="testRegister()">Test Registration</button>
                    <div id="register-result" class="code-block" style="display: none;"></div>
                </div>
                
                <div class="api-card">
                    <h3>🔐 User Login</h3>
                    <p>Test user login endpoint</p>
                    <button class="btn btn-primary" onclick="testLogin()">Test Login</button>
                    <div id="login-result" class="code-block" style="display: none;"></div>
                </div>
            </div>
        </section>

        <section class="section">
            <h2>📚 Available Features</h2>
            <ul class="feature-list">
                <li>Rate-limited Facebook Marketing API integration</li>
                <li>Comprehensive caching system (60-minute duration)</li>
                <li>JWT-based authentication system</li>
                <li>Ad accounts, campaigns, ad sets, and ads management</li>
                <li>Lead forms integration</li>
                <li>Real-time API health monitoring</li>
                <li>Smart batching for API efficiency</li>
                <li>Error handling and retry logic</li>
                <li>CORS-enabled for frontend integration</li>
                <li>Production-ready architecture</li>
            </ul>
        </section>

        <section class="section">
            <h2>🔗 Quick Links</h2>
            <div class="api-grid">
                <div class="api-card">
                    <h3>📖 API Documentation</h3>
                    <p>Complete API documentation and endpoints</p>
                    <a href="/api-docs" class="btn btn-primary" target="_blank">View Docs</a>
                </div>
                
                <div class="api-card">
                    <h3>⚛️ React Frontend</h3>
                    <p>Full-featured React testing interface</p>
                    <a href="http://localhost:3001" class="btn btn-primary" target="_blank">Open Frontend</a>
                </div>
                
                <div class="api-card">
                    <h3>🏠 Main Dashboard</h3>
                    <p>Modern project management dashboard</p>
                    <a href="/" class="btn btn-secondary">Go to Dashboard</a>
                </div>
            </div>
        </section>
    </div>

    <script>
        // API testing functions
        async function checkHealth() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                document.getElementById('health-result').style.display = 'block';
                document.getElementById('health-result').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('health-result').style.display = 'block';
                document.getElementById('health-result').textContent = 'Error: ' + error.message;
            }
        }

        async function clearCache() {
            try {
                const response = await fetch('/api/v1/cache/clear', { method: 'POST' });
                const data = await response.json();
                document.getElementById('cache-result').style.display = 'block';
                document.getElementById('cache-result').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('cache-result').style.display = 'block';
                document.getElementById('cache-result').textContent = 'Error: ' + error.message;
            }
        }

        async function getFacebookOAuth() {
            try {
                const response = await fetch('/api/v1/facebook/oauth-url?redirectUri=http://localhost:3000/testing');
                const data = await response.json();
                document.getElementById('oauth-result').style.display = 'block';
                document.getElementById('oauth-result').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('oauth-result').style.display = 'block';
                document.getElementById('oauth-result').textContent = 'Error: ' + error.message;
            }
        }

        async function getAdAccounts() {
            try {
                const response = await fetch('/api/v1/facebook/ad-accounts');
                const data = await response.json();
                document.getElementById('accounts-result').style.display = 'block';
                document.getElementById('accounts-result').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('accounts-result').style.display = 'block';
                document.getElementById('accounts-result').textContent = 'Error: ' + error.message;
            }
        }

        async function getCampaigns() {
            try {
                const response = await fetch('/api/v1/facebook/campaigns-nested');
                const data = await response.json();
                document.getElementById('campaigns-result').style.display = 'block';
                document.getElementById('campaigns-result').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('campaigns-result').style.display = 'block';
                document.getElementById('campaigns-result').textContent = 'Error: ' + error.message;
            }
        }

        async function getLeadForms() {
            try {
                const response = await fetch('/api/v1/facebook/leadgen-forms');
                const data = await response.json();
                document.getElementById('leadforms-result').style.display = 'block';
                document.getElementById('leadforms-result').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('leadforms-result').style.display = 'block';
                document.getElementById('leadforms-result').textContent = 'Error: ' + error.message;
            }
        }

        async function testRegister() {
            try {
                const response = await fetch('/api/v1/auth/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'testpassword',
                        firstName: 'Test',
                        lastName: 'User'
                    })
                });
                const data = await response.json();
                document.getElementById('register-result').style.display = 'block';
                document.getElementById('register-result').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('register-result').style.display = 'block';
                document.getElementById('register-result').textContent = 'Error: ' + error.message;
            }
        }

        async function testLogin() {
            try {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'testpassword'
                    })
                });
                const data = await response.json();
                document.getElementById('login-result').style.display = 'block';
                document.getElementById('login-result').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('login-result').style.display = 'block';
                document.getElementById('login-result').textContent = 'Error: ' + error.message;
            }
        }
    </script>
    <script src="/components/shared-utils.js"></script>
</body>
</html>
