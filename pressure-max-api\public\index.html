<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pressure Max Dashboard</title>
    <link rel="stylesheet" href="/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css">
</head>
<body>
    <div class="app">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">🚀</div>
                    <span class="logo-text">Pressure Max</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-label">Favorites</div>
                    <ul class="nav-list">
                        <li class="nav-item active">
                            <a href="#" class="nav-link">
                                <i data-lucide="star"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i data-lucide="zap"></i>
                                <span>Campaign Manager</span>
                            </a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-section">
                    <div class="nav-label">All projects</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="/facebook-ads" class="nav-link">
                                <i data-lucide="target"></i>
                                <span>Facebook Ads</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i data-lucide="users"></i>
                                <span>Lead Management</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i data-lucide="phone"></i>
                                <span>VAPI Integration</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i data-lucide="credit-card"></i>
                                <span>Stripe Payments</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/testing" class="nav-link">
                                <i data-lucide="flask"></i>
                                <span>API Testing</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <div class="sidebar-footer">
                <button class="new-project-btn">
                    <i data-lucide="plus"></i>
                    New project
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i data-lucide="menu"></i>
                    </button>
                    <div class="search-container">
                        <i data-lucide="search"></i>
                        <input type="text" placeholder="Search..." class="search-input">
                    </div>
                </div>
                
                <div class="header-right">
                    <button class="header-btn">
                        <i data-lucide="bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <button class="header-btn">
                        <i data-lucide="settings"></i>
                    </button>
                    <button class="header-btn logout-btn" onclick="handleLogout()" title="Logout">
                        <i data-lucide="log-out"></i>
                    </button>
                    <div class="user-avatar">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="User">
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="dashboard">
                <div class="dashboard-header">
                    <div class="dashboard-title">
                        <h1>Facebook Marketing Dashboard</h1>
                        <p>Monitor and manage your Facebook advertising campaigns</p>
                    </div>
                    
                    <div class="dashboard-actions">
                        <button class="btn btn-secondary">
                            <i data-lucide="download"></i>
                            Export
                        </button>
                        <button class="btn btn-primary">
                            <i data-lucide="plus"></i>
                            Create Campaign
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Active Campaigns</span>
                            <i data-lucide="trending-up" class="stat-icon"></i>
                        </div>
                        <div class="stat-value">12</div>
                        <div class="stat-change positive">+2 this week</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Total Spend</span>
                            <i data-lucide="dollar-sign" class="stat-icon"></i>
                        </div>
                        <div class="stat-value">$24,580</div>
                        <div class="stat-change positive">+12% vs last month</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Leads Generated</span>
                            <i data-lucide="users" class="stat-icon"></i>
                        </div>
                        <div class="stat-value">1,247</div>
                        <div class="stat-change positive">+8% vs last month</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-label">Conversion Rate</span>
                            <i data-lucide="target" class="stat-icon"></i>
                        </div>
                        <div class="stat-value">3.2%</div>
                        <div class="stat-change negative">-0.3% vs last month</div>
                    </div>
                </div>

                <!-- Project Board -->
                <div class="project-board">
                    <div class="board-header">
                        <h2>Campaign Pipeline</h2>
                        <div class="board-controls">
                            <button class="btn btn-sm">
                                <i data-lucide="filter"></i>
                                Filter
                            </button>
                            <button class="btn btn-sm">
                                <i data-lucide="calendar"></i>
                                Date Range
                            </button>
                        </div>
                    </div>
                    
                    <div class="kanban-board">
                        <div class="kanban-column">
                            <div class="column-header">
                                <span class="column-title">Planning</span>
                                <span class="column-count">3</span>
                            </div>
                            <div class="column-content">
                                <div class="task-card">
                                    <div class="task-header">
                                        <span class="task-category ui-design">Strategy</span>
                                        <button class="task-menu">⋯</button>
                                    </div>
                                    <h3 class="task-title">Q1 Lead Generation Campaign</h3>
                                    <p class="task-description">Plan comprehensive lead generation strategy for Q1 targeting small businesses</p>
                                    <div class="task-footer">
                                        <div class="task-avatars">
                                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=24&h=24&fit=crop&crop=face" alt="User">
                                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=24&h=24&fit=crop&crop=face" alt="User">
                                        </div>
                                        <div class="task-stats">
                                            <span><i data-lucide="message-circle"></i> 2</span>
                                            <span><i data-lucide="paperclip"></i> 5</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="task-card">
                                    <div class="task-header">
                                        <span class="task-category marketing">Creative</span>
                                        <button class="task-menu">⋯</button>
                                    </div>
                                    <h3 class="task-title">Video Ad Creative Development</h3>
                                    <p class="task-description">Create engaging video content for Facebook and Instagram ads</p>
                                    <div class="task-footer">
                                        <div class="task-avatars">
                                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=24&h=24&fit=crop&crop=face" alt="User">
                                        </div>
                                        <div class="task-stats">
                                            <span><i data-lucide="message-circle"></i> 4</span>
                                            <span><i data-lucide="paperclip"></i> 12</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="kanban-column">
                            <div class="column-header">
                                <span class="column-title">In Progress</span>
                                <span class="column-count">2</span>
                            </div>
                            <div class="column-content">
                                <div class="task-card">
                                    <div class="task-header">
                                        <span class="task-category ux-research">Campaign</span>
                                        <button class="task-menu">⋯</button>
                                    </div>
                                    <h3 class="task-title">Holiday Season Campaign</h3>
                                    <p class="task-description">Launch and optimize holiday promotional campaigns across all platforms</p>
                                    <div class="task-footer">
                                        <div class="task-avatars">
                                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=24&h=24&fit=crop&crop=face" alt="User">
                                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=24&h=24&fit=crop&crop=face" alt="User">
                                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=24&h=24&fit=crop&crop=face" alt="User">
                                        </div>
                                        <div class="task-stats">
                                            <span><i data-lucide="message-circle"></i> 8</span>
                                            <span><i data-lucide="paperclip"></i> 15</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="kanban-column">
                            <div class="column-header">
                                <span class="column-title">Review</span>
                                <span class="column-count">1</span>
                            </div>
                            <div class="column-content">
                                <div class="task-card">
                                    <div class="task-header">
                                        <span class="task-category testing">Analysis</span>
                                        <button class="task-menu">⋯</button>
                                    </div>
                                    <h3 class="task-title">Campaign Performance Analysis</h3>
                                    <p class="task-description">Analyze Q4 campaign performance and prepare optimization recommendations</p>
                                    <div class="task-footer">
                                        <div class="task-avatars">
                                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=24&h=24&fit=crop&crop=face" alt="User">
                                        </div>
                                        <div class="task-stats">
                                            <span><i data-lucide="message-circle"></i> 3</span>
                                            <span><i data-lucide="paperclip"></i> 8</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="kanban-column">
                            <div class="column-header">
                                <span class="column-title">Complete</span>
                                <span class="column-count">4</span>
                            </div>
                            <div class="column-content">
                                <div class="task-card">
                                    <div class="task-header">
                                        <span class="task-category graphic-design">Setup</span>
                                        <button class="task-menu">⋯</button>
                                    </div>
                                    <h3 class="task-title">Facebook Pixel Integration</h3>
                                    <p class="task-description">Implement Facebook Pixel tracking across all landing pages</p>
                                    <div class="task-footer">
                                        <div class="task-avatars">
                                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=24&h=24&fit=crop&crop=face" alt="User">
                                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=24&h=24&fit=crop&crop=face" alt="User">
                                        </div>
                                        <div class="task-stats">
                                            <span><i data-lucide="message-circle"></i> 6</span>
                                            <span><i data-lucide="paperclip"></i> 3</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="/script.js"></script>
</body>
</html>
