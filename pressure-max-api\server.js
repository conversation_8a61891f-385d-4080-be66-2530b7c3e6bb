const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const cookieParser = require('cookie-parser');
const { requireAuth, handleLogin, handleLogout, getSessionInfo } = require('./middleware/sessionAuth');
const port = 3000;

// Rate limiting and caching setup
const cache = new Map();
const CACHE_DURATION = 60 * 60 * 1000; // 60 minutes cache (much longer to reduce API calls)
const API_DELAY = 10000; // 10 second delay between API calls (much more conservative)
const MAX_RETRIES = 1; // Only 1 retry to avoid hitting limits
const RETRY_DELAY_BASE = 30000; // 30 seconds base delay for retries
let lastApiCall = 0;
let apiCallQueue = Promise.resolve(); // Global queue to serialize all API calls

// Enhanced rate limiting helper with exponential backoff and global queue
async function rateLimitedApiCall(apiCallFunction, retryCount = 0) {
  // Add to global queue to ensure sequential execution
  return apiCallQueue = apiCallQueue.then(async () => {
    const now = Date.now();
    const timeSinceLastCall = now - lastApiCall;

    if (timeSinceLastCall < API_DELAY) {
      const waitTime = API_DELAY - timeSinceLastCall;
      console.log(`⏱️ Rate limiting: waiting ${waitTime}ms before next API call`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    lastApiCall = Date.now();

    try {
      return await apiCallFunction();
    } catch (error) {
      // Check if it's a rate limit error
      if (error.response?.data?.error?.code === 17 ||
          error.response?.data?.error?.error_subcode === 2446079) {

        if (retryCount < MAX_RETRIES) {
          const retryDelay = RETRY_DELAY_BASE * Math.pow(2, retryCount); // Exponential backoff
          console.log(`🔄 Rate limit hit, retrying in ${retryDelay}ms (attempt ${retryCount + 1}/${MAX_RETRIES})`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          return rateLimitedApiCall(apiCallFunction, retryCount + 1);
        } else {
          console.log(`❌ Max retries reached for rate limited request`);
          throw error;
        }
      }

      // If it's not a rate limit error, throw it immediately
      throw error;
    }
  });
}

// Cache helper functions
function getCacheKey(endpoint, params) {
  return `${endpoint}_${JSON.stringify(params)}`;
}

function getFromCache(key) {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log(`💾 Cache hit for ${key}`);
    return cached.data;
  }
  return null;
}

function setCache(key, data) {
  cache.set(key, {
    data: data,
    timestamp: Date.now()
  });
  console.log(`💾 Cached data for ${key}`);
}

// Clear all cache entries related to a specific ad account
function clearAdAccountCache(adAccountId) {
  const keysToDelete = [];
  for (const [key] of cache) {
    // Check if the cache key contains the ad account ID in the JSON params
    if (key.includes(adAccountId)) {
      keysToDelete.push(key);
    }
  }

  // Also specifically clear the main cache keys we know about
  const specificKeys = [
    getCacheKey('campaigns', { adAccountId }),
    getCacheKey('complete-account-data', { adAccountId }),
    getCacheKey('adsets', { adAccountId }),
    getCacheKey('ads', { adAccountId }),
    getCacheKey('leadforms', { adAccountId }),
    getCacheKey('audiences', { adAccountId })
  ];

  specificKeys.forEach(key => {
    if (cache.has(key)) {
      keysToDelete.push(key);
    }
  });

  // Remove duplicates
  const uniqueKeys = [...new Set(keysToDelete)];

  uniqueKeys.forEach(key => {
    cache.delete(key);
    console.log(`🗑️ Cleared cache entry: ${key}`);
  });

  console.log(`🗑️ Cleared ${uniqueKeys.length} cache entries for ad account: ${adAccountId}`);
}

// Helper function to ensure ad account ID has proper format
function ensureActPrefix(adAccountId) {
  if (!adAccountId) return adAccountId;
  // If it already starts with 'act_', return as is
  if (adAccountId.startsWith('act_')) return adAccountId;
  // If it's just the numeric ID, add 'act_' prefix
  return `act_${adAccountId}`;
}

// Helper function to format targeting data for better readability
function formatTargetingData(targeting) {
  if (!targeting) return null;

  const formatted = {
    demographics: {},
    geographic: {},
    interests: [],
    behaviors: [],
    custom_audiences: [],
    lookalike_audiences: [],
    device_platforms: [],
    placements: [],
    connections: {},
    automation: {},
    raw: targeting // Keep original data for debugging
  };

  try {
    // Demographics
    if (targeting.age_min !== undefined) formatted.demographics.age_min = targeting.age_min;
    if (targeting.age_max !== undefined) formatted.demographics.age_max = targeting.age_max;
    if (targeting.genders) formatted.demographics.genders = targeting.genders;

    // Geographic targeting with detailed location information
    if (targeting.geo_locations) {
      const geo = targeting.geo_locations;

      // Countries with names
      if (geo.countries) {
        formatted.geographic.countries = geo.countries;
      }

      // Regions with detailed info
      if (geo.regions) {
        formatted.geographic.regions = geo.regions.map(region => ({
          key: region.key,
          name: region.name || region.key
        }));
      }

      // Cities with radius information
      if (geo.cities) {
        formatted.geographic.cities = geo.cities.map(city => ({
          key: city.key,
          name: city.name || city.key,
          radius: city.radius,
          distance_unit: city.distance_unit || 'mile'
        }));
      }

      // Custom locations with coordinates and radius
      if (geo.custom_locations) {
        formatted.geographic.custom_locations = geo.custom_locations.map(location => ({
          latitude: location.latitude,
          longitude: location.longitude,
          radius: location.radius,
          distance_unit: location.distance_unit || 'mile',
          name: location.name || `${location.latitude}, ${location.longitude}`
        }));
      }

      // Location types (home, recent, travel_in)
      if (geo.location_types) {
        formatted.geographic.location_types = geo.location_types;
      }

      // ZIP codes
      if (geo.zips) {
        formatted.geographic.zips = geo.zips;
      }
    }

    // Detailed interests with full information
    if (targeting.interests) {
      formatted.interests = targeting.interests.map(interest => ({
        id: interest.id,
        name: interest.name,
        topic: interest.topic || null,
        path: interest.path || []
      }));
    }

    // Detailed behaviors
    if (targeting.behaviors) {
      formatted.behaviors = targeting.behaviors.map(behavior => ({
        id: behavior.id,
        name: behavior.name,
        path: behavior.path || []
      }));
    }

    // Custom audiences with detailed info
    if (targeting.custom_audiences) {
      formatted.custom_audiences = targeting.custom_audiences.map(audience => ({
        id: audience.id,
        name: audience.name,
        subtype: audience.subtype || null
      }));
    }

    // Lookalike audiences with detailed info
    if (targeting.lookalike_audiences) {
      formatted.lookalike_audiences = targeting.lookalike_audiences.map(audience => ({
        id: audience.id,
        name: audience.name,
        ratio: audience.ratio,
        country: audience.country || null
      }));
    }

    // Device platforms with detailed breakdown
    if (targeting.device_platforms) {
      formatted.device_platforms = targeting.device_platforms;
    }

    // Detailed placements information
    if (targeting.publisher_platforms) {
      formatted.placements = targeting.publisher_platforms;
    }

    // Facebook placements (more detailed)
    if (targeting.facebook_positions) {
      formatted.facebook_positions = targeting.facebook_positions;
    }

    // Instagram placements
    if (targeting.instagram_positions) {
      formatted.instagram_positions = targeting.instagram_positions;
    }

    // Messenger placements
    if (targeting.messenger_positions) {
      formatted.messenger_positions = targeting.messenger_positions;
    }

    // Automation and advanced settings
    if (targeting.targeting_automation) {
      formatted.automation.targeting_automation = targeting.targeting_automation;
    }

    // Advantage+ audience settings
    if (targeting.advantage_audience !== undefined) {
      formatted.automation.advantage_audience = targeting.advantage_audience;
    }

    // Connections (include/exclude)
    if (targeting.connections) formatted.connections.include = targeting.connections;
    if (targeting.excluded_connections) formatted.connections.exclude = targeting.excluded_connections;

    return formatted;
  } catch (error) {
    console.error('Error formatting targeting data:', error);
    return { raw: targeting, error: 'Failed to format targeting data' };
  }
}

// Helper function to format creative data for better readability
function formatCreativeData(creative) {
  if (!creative) return null;

  const formatted = {
    basic_info: {},
    media_assets: {},
    copy_elements: {},
    call_to_action: {},
    destination: {},
    raw: creative // Keep original data for debugging
  };

  try {
    // Basic creative information
    formatted.basic_info = {
      id: creative.id,
      name: creative.name || 'Unnamed Creative',
      type: creative.object_type || 'Unknown'
    };

    // Media assets (images, videos, carousels)
    formatted.media_assets = {
      image_url: creative.image_url || null,
      image_hash: creative.image_hash || null,
      video_id: creative.video_id || null,
      thumbnail_url: creative.thumbnail_url || null,
      object_id: creative.object_id || null
    };

    // Copy elements (text content)
    formatted.copy_elements = {
      title: creative.title || null,
      body: creative.body || null,
      primary_text: null,
      headline: null,
      description: null,
      link_description: null
    };

    // Extract detailed copy from object_story_spec if available
    if (creative.object_story_spec) {
      const story = creative.object_story_spec;

      // Link data contains most of the copy elements
      if (story.link_data) {
        const linkData = story.link_data;
        formatted.copy_elements.primary_text = linkData.message || null;
        formatted.copy_elements.headline = linkData.name || null;
        formatted.copy_elements.description = linkData.description || null;
        formatted.copy_elements.link_description = linkData.caption || null;

        // Media from link_data
        if (linkData.picture) {
          formatted.media_assets.link_picture = linkData.picture;
        }
        if (linkData.video_id) {
          formatted.media_assets.link_video_id = linkData.video_id;
        }

        // Call to action from link_data
        if (linkData.call_to_action) {
          formatted.call_to_action = {
            type: linkData.call_to_action.type || null,
            value: linkData.call_to_action.value || {}
          };
        }
      }

      // Video data
      if (story.video_data) {
        const videoData = story.video_data;
        formatted.copy_elements.primary_text = videoData.message || formatted.copy_elements.primary_text;
        formatted.copy_elements.headline = videoData.title || formatted.copy_elements.headline;
        formatted.media_assets.video_id = videoData.video_id || formatted.media_assets.video_id;

        if (videoData.call_to_action) {
          formatted.call_to_action = {
            type: videoData.call_to_action.type || null,
            value: videoData.call_to_action.value || {}
          };
        }
      }

      // Photo data
      if (story.photo_data) {
        const photoData = story.photo_data;
        formatted.copy_elements.primary_text = photoData.message || formatted.copy_elements.primary_text;
        formatted.media_assets.photo_url = photoData.url || null;
      }
    }

    // Call to action information
    if (creative.call_to_action && !formatted.call_to_action.type) {
      formatted.call_to_action = {
        type: creative.call_to_action.type || null,
        value: creative.call_to_action.value || {}
      };
    }

    // Destination URL information
    formatted.destination = {
      link_url: creative.link_url || null,
      object_url: creative.object_url || null,
      website_url: null,
      display_link: null
    };

    // Extract website URL from call_to_action value
    if (formatted.call_to_action.value && formatted.call_to_action.value.link) {
      formatted.destination.website_url = formatted.call_to_action.value.link;
    }

    // Extract display link
    if (formatted.destination.link_url) {
      try {
        const url = new URL(formatted.destination.link_url);
        formatted.destination.display_link = url.hostname + (url.pathname !== '/' ? url.pathname : '');
      } catch (e) {
        formatted.destination.display_link = formatted.destination.link_url;
      }
    }

    return formatted;
  } catch (error) {
    console.error('Error formatting creative data:', error);
    return { raw: creative, error: 'Failed to format creative data' };
  }
}

// Basic middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3001',
  credentials: true
}));

// Cookie parser for session management
app.use(cookieParser());

// Static file serving
app.use(express.static(path.join(__dirname, 'public')));

app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0',
    rateLimit: {
      delay: API_DELAY,
      lastCall: lastApiCall
    }
  });
});

// Cache management endpoint
app.post('/api/v1/cache/clear', (req, res) => {
  cache.clear();
  console.log('🗑️ Cache cleared manually');
  res.json({
    status: 'OK',
    message: 'Cache cleared successfully',
    timestamp: new Date().toISOString()
  });
});

// Rate limit status endpoint
app.get('/api/v1/rate-limit-status', (req, res) => {
  const now = Date.now();
  const timeSinceLastCall = now - lastApiCall;
  const nextCallAvailable = Math.max(0, API_DELAY - timeSinceLastCall);

  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    rate_limiting: {
      api_delay_ms: API_DELAY,
      cache_duration_ms: CACHE_DURATION,
      time_since_last_call_ms: timeSinceLastCall,
      next_call_available_in_ms: nextCallAvailable,
      max_retries: MAX_RETRIES,
      retry_delay_base_ms: RETRY_DELAY_BASE,
      recommendation: nextCallAvailable > 0 ?
        `Wait ${Math.ceil(nextCallAvailable / 1000)} seconds before next API call` :
        'API calls can be made now'
    }
  });
});

// Basic auth endpoints
app.post('/api/v1/auth/register', (req, res) => {
  console.log('Register request:', req.body);
  res.status(201).json({
    message: 'User registered successfully',
    user: {
      id: 'user_123',
      email: req.body.email,
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      role: 'user'
    },
    tokens: {
      accessToken: 'demo_access_token_123',
      refreshToken: 'demo_refresh_token_123'
    }
  });
});

// Updated authentication endpoints with session management
app.post('/api/v1/auth/login', handleLogin);
app.post('/api/v1/auth/logout', handleLogout);

// Session info endpoint (for debugging)
app.get('/api/v1/auth/session-info', requireAuth, (req, res) => {
  res.json({
    session: req.session,
    user: req.user,
    sessionInfo: getSessionInfo()
  });
});

// Session validation endpoint (no auth required - for checking if logged in)
app.get('/api/v1/auth/check-session', (req, res) => {
  const sessionId = req.cookies?.[require('./middleware/sessionAuth').COOKIE_NAME];
  const { getSession } = require('./middleware/sessionAuth');

  if (!sessionId) {
    return res.json({ authenticated: false, reason: 'No session cookie' });
  }

  const session = getSession(sessionId);
  if (!session) {
    return res.json({ authenticated: false, reason: 'Invalid or expired session' });
  }

  res.json({
    authenticated: true,
    user: {
      id: session.userId,
      email: session.userEmail
    },
    session: {
      id: session.id,
      expiresAt: session.expiresAt,
      lastAccessed: session.lastAccessed
    }
  });
});

// User profile endpoint
app.get('/api/v1/users/profile', async (req, res) => {
  try {
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    // Get real Facebook user profile
    const axios = require('axios');
    const response = await axios.get('https://graph.facebook.com/v23.0/me', {
      params: {
        fields: 'id,name,email,first_name,last_name,picture',
        access_token: accessToken
      }
    });

    const fbUser = response.data;
    console.log('Facebook User Profile:', fbUser);

    res.json({
      id: `fb_${fbUser.id}`,
      email: fbUser.email || '<EMAIL>',
      firstName: fbUser.first_name || 'Facebook',
      lastName: fbUser.last_name || 'User',
      name: fbUser.name,
      role: 'user',
      isActive: true,
      lastLoginAt: new Date().toISOString(),
      picture: fbUser.picture?.data?.url,
      facebookId: fbUser.id
    });
  } catch (error) {
    console.error('Facebook user profile error:', error.response?.data || error.message);

    // Fallback to demo data if API fails
    res.json({
      id: 'user_fb_demo',
      email: '<EMAIL>',
      firstName: 'Facebook',
      lastName: 'User',
      role: 'user',
      isActive: true,
      lastLoginAt: new Date().toISOString(),
      error: 'Real API call failed, showing demo data'
    });
  }
});

// Facebook Public endpoints (for OAuth and public access)
app.get('/api/v1/facebook-public/oauth-url', (req, res) => {
  const { redirectUri } = req.query;
  const state = 'demo_state_' + Date.now();

  // Real Facebook OAuth URL with actual app ID
  const permissions = [
    'ads_management',
    'ads_read',
    'read_insights',
    'pages_read_engagement',
    'pages_manage_ads',
    'business_management'
  ].join(',');

  const oauthUrl = `https://www.facebook.com/v23.0/dialog/oauth?client_id=394349039883481&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${permissions}&state=${state}&response_type=code`;

  res.json({
    success: true,
    oauthUrl: oauthUrl,
    state: state
  });
});

app.post('/api/v1/facebook-public/oauth-callback', (req, res) => {
  const { code, state, redirectUri } = req.body;

  // In a real implementation, you would exchange the code for an access token
  // For now, return a demo response with the expected structure
  res.json({
    success: true,
    accessToken: 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY',
    profile: {
      id: '****************',
      name: 'Demo User',
      email: '<EMAIL>'
    },
    tokenType: 'bearer',
    expiresIn: 5183944
  });
});

app.post('/api/v1/facebook-public/test-token', (req, res) => {
  const { accessToken } = req.body;

  // In a real implementation, you would test the token with Facebook
  res.json({
    success: true,
    valid: true,
    user_id: '****************',
    scopes: ['ads_management', 'ads_read', 'read_insights']
  });
});

app.post('/api/v1/facebook-public/ad-accounts', async (req, res) => {
  try {
    // Redirect to the existing ad-accounts endpoint
    const response = await fetch(`http://localhost:3000/api/v1/facebook/ad-accounts`);
    const data = await response.json();
    res.json(data);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch ad accounts' });
  }
});

app.post('/api/v1/facebook-public/campaigns', async (req, res) => {
  try {
    const { accessToken, ...campaignData } = req.body;

    // Redirect to the existing campaign creation endpoint
    const response = await fetch(`http://localhost:3000/api/v1/facebook/campaign-hierarchy`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(campaignData)
    });

    const data = await response.json();
    res.json(data);
  } catch (error) {
    res.status(500).json({ error: 'Failed to create campaign' });
  }
});

// Facebook endpoints (original)
app.get('/api/v1/facebook/oauth-url', (req, res) => {
  const { redirectUri } = req.query;
  const state = 'demo_state_' + Date.now();

  // Real Facebook OAuth URL with actual app ID
  const permissions = [
    'ads_management',
    'ads_read',
    'business_management',
    'pages_read_engagement',
    'pages_manage_ads',
    'leads_retrieval',
    'email',
    'public_profile'
  ].join(',');

  const oauthUrl = `https://www.facebook.com/v18.0/dialog/oauth?` +
    `client_id=394349039883481&` +
    `redirect_uri=${encodeURIComponent(redirectUri || 'http://localhost:3001/facebook-callback')}&` +
    `scope=${encodeURIComponent(permissions)}&` +
    `response_type=code&` +
    `state=${state}`;

  console.log('Generated Facebook OAuth URL:', oauthUrl);

  res.json({
    oauthUrl,
    state
  });
});

// Facebook OAuth callback handler
app.post('/api/v1/facebook/oauth-callback', (req, res) => {
  const { code, state } = req.body;
  console.log('Facebook OAuth callback received:', { code: code?.substring(0, 20) + '...', state });

  // In a real implementation, you would:
  // 1. Exchange code for access token
  // 2. Get user profile from Facebook
  // 3. Create or update user in database
  // 4. Generate JWT tokens

  // For demo, return success with mock user data
  res.json({
    message: 'Facebook account connected successfully',
    user: {
      id: 'user_fb_' + Date.now(),
      email: '<EMAIL>',
      firstName: 'Facebook',
      lastName: 'User',
      role: 'user',
      facebookConnected: true
    },
    tokens: {
      accessToken: 'demo_fb_access_token_' + Date.now(),
      refreshToken: 'demo_fb_refresh_token_' + Date.now()
    },
    profile: {
      id: '*****************',
      name: 'Facebook User',
      email: '<EMAIL>'
    },
    permissions: [
      'ads_management',
      'ads_read',
      'business_management',
      'pages_read_engagement',
      'pages_manage_ads',
      'leads_retrieval',
      'email',
      'public_profile'
    ]
  });
});

app.get('/api/v1/facebook/ad-accounts', async (req, res) => {
  try {
    console.log('🔍 Fetching Facebook ad accounts...');

    // Check cache first
    const cacheKey = getCacheKey('ad-accounts', {});
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('💾 Returning cached ad accounts data');
      return res.json(cachedData);
    }

    // Use the real Facebook access token
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    // Make rate-limited API call to Facebook
    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get('https://graph.facebook.com/v23.0/me/adaccounts', {
        params: {
          fields: 'id,name,account_status,currency,timezone_name,business,account_id',
          access_token: accessToken,
          limit: 25 // Reduced limit to avoid rate limits
        }
      });
    });

    console.log('✅ Facebook ad accounts retrieved:', response.data.data?.length || 0, 'accounts');

    const adAccounts = response.data.data || [];

    // Cache the result
    setCache(cacheKey, adAccounts);

    res.json(adAccounts);
  } catch (error) {
    console.error('❌ Facebook ad accounts error:', error.response?.data || error.message);

    // Fallback to demo data if API fails
    const fallbackData = [
      {
        id: 'act_demo',
        account_id: 'demo_account',
        name: 'Demo Ad Account (API Error)',
        account_status: 'ACTIVE',
        currency: 'USD',
        timezone_name: 'America/New_York',
        error: 'Real API call failed, showing demo data'
      }
    ];

    res.json(fallbackData);
  }
});

app.get('/api/v1/facebook/pages', async (req, res) => {
  try {
    // Use the real Facebook access token
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    // Make real API call to Facebook
    const axios = require('axios');
    const response = await axios.get('https://graph.facebook.com/v23.0/me/accounts', {
      params: {
        fields: 'id,name,category,access_token,picture',
        access_token: accessToken
      }
    });

    console.log('Facebook Pages Response:', response.data);
    res.json(response.data.data || []);
  } catch (error) {
    console.error('Facebook Pages API Error:', error.response?.data || error.message);

    // Fallback to demo data if API fails
    res.json([
      {
        id: 'page_demo',
        name: 'Demo Business Page (API Error)',
        category: 'Business',
        error: 'Real API call failed, showing demo data'
      }
    ]);
  }
});

app.post('/api/v1/facebook/campaigns', async (req, res) => {
  try {
    const { adAccountId, name, objective, status = 'PAUSED' } = req.body;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    if (!adAccountId || !name || !objective) {
      return res.status(400).json({
        error: 'Missing required fields: adAccountId, name, objective'
      });
    }

    console.log('🚀 Creating Facebook campaign:', { adAccountId, name, objective, status });

    // Ensure ad account ID has proper format
    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('📝 Using formatted ad account ID:', formattedAdAccountId);

    // Make rate-limited API call to Facebook to create campaign
    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/campaigns`, null, {
        params: {
          name: name,
          objective: objective,
          status: status,
          special_ad_categories: '[]', // Required parameter for campaign creation
          access_token: accessToken
        }
      });
    });

    console.log('✅ Facebook campaign created successfully:', response.data);

    // Clear ALL cache entries related to this ad account to force refresh
    clearAdAccountCache(formattedAdAccountId);

    res.status(201).json({
      message: 'Campaign created successfully in Facebook',
      campaign: {
        id: response.data.id,
        name: name,
        objective: objective,
        status: status,
        created_time: new Date().toISOString(),
        facebook_id: response.data.id
      }
    });
  } catch (error) {
    console.error('Facebook campaign creation error:', error.response?.data || error.message);

    res.status(500).json({
      error: 'Failed to create campaign in Facebook',
      details: error.response?.data || error.message,
      fallback_campaign: {
        id: 'demo_' + Date.now(),
        name: req.body.name,
        objective: req.body.objective,
        status: 'DEMO_MODE',
        created_time: new Date().toISOString(),
        note: 'Campaign creation failed, this is demo data'
      }
    });
  }
});

app.get('/api/v1/facebook/campaigns/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🚀 SMART BATCHING: Fetching complete account data for:', adAccountId);

    // Ensure ad account ID has proper format
    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('📝 Using formatted ad account ID:', formattedAdAccountId);

    // Check cache first - using new cache key for complete data
    const cacheKey = getCacheKey('complete-account-data', { adAccountId: formattedAdAccountId });
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('💾 Returning cached complete account data');
      return res.json(cachedData);
    }

    // Make single smart batching API call to get everything in one request
    const axios = require('axios');
    console.log('🎯 Making ONE API call to fetch campaigns + ad sets + ads + insights');

    const response = await rateLimitedApiCall(async () => {
      return await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/campaigns`, {
        params: {
          fields: 'id,name,status,objective,created_time,updated_time,start_time,stop_time,daily_budget,lifetime_budget,budget_remaining,configured_status,effective_status,adsets{id,name,status,created_time,updated_time,daily_budget,lifetime_budget,budget_remaining,targeting,optimization_goal,billing_event,bid_strategy,configured_status,effective_status,ads{id,name,status,created_time,updated_time,creative{id,name,title,body,image_url,video_id,thumbnail_url,image_hash,object_story_spec{link_data{message,name,description,caption,picture,call_to_action},video_data{message,title,video_id,call_to_action},photo_data{message,url}},call_to_action,link_url,object_id,object_story_id,object_type,object_url},configured_status,effective_status,insights{impressions,clicks,spend,cpm,cpc,ctr,reach,frequency,actions,cost_per_action_type,unique_clicks,unique_ctr,cost_per_unique_click,social_spend,inline_link_clicks,inline_link_click_ctr,cost_per_inline_link_click}}}',
          access_token: accessToken,
          limit: 25
        }
      });
    });

    console.log('✅ SMART BATCHING SUCCESS! Retrieved:', response.data.data?.length || 0, 'campaigns with complete nested data');

    const completeData = response.data.data || [];

    // Process and format the nested data
    console.log('🔄 Processing and formatting nested data...');
    const processedData = completeData.map(campaign => {
      // Format campaign data
      const formattedCampaign = {
        ...campaign,
        adsets: (campaign.adsets?.data || []).map(adset => {
          // Format targeting data
          const formattedTargeting = formatTargetingData(adset.targeting);

          return {
            ...adset,
            targeting_formatted: formattedTargeting,
            ads: (adset.ads?.data || []).map(ad => {
              // Format creative data
              const formattedCreative = formatCreativeData(ad.creative);

              return {
                ...ad,
                creative_formatted: formattedCreative,
                insights: ad.insights?.data?.[0] || {
                  impressions: '0',
                  clicks: '0',
                  spend: '0.00',
                  cpm: '0.00',
                  cpc: '0.00',
                  ctr: '0.00',
                  reach: '0',
                  frequency: '0.00',
                  actions: [],
                  cost_per_action_type: [],
                  unique_clicks: '0',
                  unique_ctr: '0.00',
                  cost_per_unique_click: '0.00',
                  social_spend: '0.00',
                  inline_link_clicks: '0',
                  inline_link_click_ctr: '0.00',
                  cost_per_inline_link_click: '0.00'
                }
              };
            })
          };
        })
      };

      return formattedCampaign;
    });

    // Cache the complete processed result
    setCache(cacheKey, processedData);

    console.log('📊 SMART BATCHING COMPLETE - All data processed and cached in ONE API call!');
    res.json(processedData);
  } catch (error) {
    console.error('❌ SMART BATCHING ERROR:', error.response?.data || error.message);

    // Fallback to demo data with nested structure if API fails
    res.json([
      {
        id: 'demo_camp_1',
        name: 'Demo Campaign (Smart Batching API Error)',
        objective: 'REACH',
        status: 'PAUSED',
        created_time: '2024-01-15T10:00:00Z',
        daily_budget: 5000,
        adsets: [
          {
            id: 'demo_adset_1',
            name: 'Demo Ad Set',
            status: 'PAUSED',
            targeting_formatted: { raw: {}, error: 'Demo data' },
            ads: [
              {
                id: 'demo_ad_1',
                name: 'Demo Ad',
                status: 'PAUSED',
                creative_formatted: { raw: {}, error: 'Demo data' },
                insights: {
                  impressions: '0',
                  clicks: '0',
                  spend: '0.00',
                  cpm: '0.00',
                  cpc: '0.00',
                  ctr: '0.00'
                }
              }
            ]
          }
        ],
        error: 'Smart batching API call failed, showing demo data',
        error_details: error.response?.data || error.message
      }
    ]);
  }
});

// Ad Sets endpoint
app.get('/api/v1/facebook/adsets/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('Fetching ad sets for ad account:', adAccountId);

    // Ensure ad account ID has proper format
    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('Using formatted ad account ID:', formattedAdAccountId);

    // Make real API call to Facebook to get ad sets with enhanced fields including detailed targeting
    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/adsets`, {
        params: {
          fields: 'id,name,campaign_id,status,created_time,updated_time,daily_budget,lifetime_budget,budget_remaining,targeting,optimization_goal,billing_event,bid_amount,bid_strategy,configured_status,effective_status,end_time,frequency_control_specs,instagram_actor_id,is_dynamic_creative,issues_info,learning_stage_info,pacing_type,promoted_object,recommendations,recurring_budget_semantics,review_feedback,rf_prediction_id,source_adset,source_adset_id,start_time,targeting_optimization_types,time_based_ad_rotation_id_blocks,time_based_ad_rotation_intervals,use_new_app_click',
          access_token: accessToken,
          limit: 50
        }
      });
    });

    console.log('Facebook ad sets retrieved:', response.data);

    // Get performance insights for each ad set
    const adSetsWithInsights = await Promise.all(
      (response.data.data || []).map(async (adSet) => {
        try {
          const insightsResponse = await rateLimitedApiCall(async () => {
            return await axios.get(`https://graph.facebook.com/v23.0/${adSet.id}/insights`, {
              params: {
                fields: 'impressions,clicks,spend,cpm,cpc,ctr,reach,frequency,actions,cost_per_action_type,unique_clicks,unique_ctr,cost_per_unique_click',
                access_token: accessToken,
                date_preset: 'last_30d'
              }
            });
          });

          const insights = insightsResponse.data.data[0] || {};

          // Format targeting data for better frontend consumption
          const formattedTargeting = formatTargetingData(adSet.targeting);

          // Log targeting data for debugging (first few ad sets only)
          if (response.data.data.indexOf(adSet) < 3) {
            console.log(`Targeting data for ad set ${adSet.id}:`, JSON.stringify(formattedTargeting, null, 2));
          }

          return {
            ...adSet,
            targeting_formatted: formattedTargeting,
            insights: {
              impressions: insights.impressions || '0',
              clicks: insights.clicks || '0',
              spend: insights.spend || '0.00',
              cpm: insights.cpm || '0.00',
              cpc: insights.cpc || '0.00',
              ctr: insights.ctr || '0.00',
              reach: insights.reach || '0',
              frequency: insights.frequency || '0.00',
              actions: insights.actions || [],
              cost_per_action_type: insights.cost_per_action_type || [],

              unique_clicks: insights.unique_clicks || '0',
              unique_ctr: insights.unique_ctr || '0.00',
              cost_per_unique_click: insights.cost_per_unique_click || '0.00'
            }
          };
        } catch (insightsError) {
          console.log(`No insights available for ad set ${adSet.id}:`, insightsError.response?.data?.error?.message || 'Unknown error');

          // Format targeting data even when insights fail
          const formattedTargeting = formatTargetingData(adSet.targeting);

          // Log targeting data for debugging (first few ad sets only)
          if (response.data.data.indexOf(adSet) < 3) {
            console.log(`Targeting data for ad set ${adSet.id} (no insights):`, JSON.stringify(formattedTargeting, null, 2));
          }

          return {
            ...adSet,
            targeting_formatted: formattedTargeting,
            insights: {
              impressions: '0',
              clicks: '0',
              spend: '0.00',
              cpm: '0.00',
              cpc: '0.00',
              ctr: '0.00',
              reach: '0',
              frequency: '0.00',
              actions: [],
              cost_per_action_type: [],

              unique_clicks: '0',
              unique_ctr: '0.00',
              cost_per_unique_click: '0.00'
            }
          };
        }
      })
    );

    res.json(adSetsWithInsights);
  } catch (error) {
    console.error('Facebook ad sets retrieval error:', error.response?.data || error.message);

    // Check if it's a rate limit error
    if (error.response?.data?.error?.code === 17) {
      console.log('🚫 Rate limit reached for ad sets. Try again in 15-30 minutes.');

      // Return a more informative message for rate limiting
      res.json([
        {
          id: 'rate_limit_notice',
          name: 'Rate Limit Reached - Try Again Later',
          campaign_id: 'rate_limit_info',
          status: 'PAUSED',
          created_time: new Date().toISOString(),
          daily_budget: 0,
          targeting_formatted: {
            demographics: {},
            geographic: {},
            interests: [],
            behaviors: [],
            custom_audiences: [],
            lookalike_audiences: [],
            device_platforms: [],
            placements: [],
            connections: {}
          },
          error: 'Facebook API rate limit reached',
          error_details: 'Too many API calls. Please wait 15-30 minutes before trying again.',
          retry_suggestion: 'The system will automatically retry with longer delays. Ad sets data will be available once rate limits reset.'
        }
      ]);
    } else {
      // Fallback to demo data for other errors
      res.json([
        {
          id: 'demo_adset_1',
          name: 'Demo Ad Set (API Error)',
          campaign_id: 'demo_campaign_1',
          status: 'PAUSED',
          created_time: '2024-01-15T10:00:00Z',
          daily_budget: 2000,
          targeting_formatted: {
            demographics: { age_min: 18, age_max: 65, genders: [1, 2] },
            geographic: { countries: ['US'] },
            interests: [{ id: 'demo_interest', name: 'Demo Interest' }],
            behaviors: [],
            custom_audiences: [],
            lookalike_audiences: [],
            device_platforms: ['mobile', 'desktop'],
            placements: ['facebook', 'instagram'],
            connections: {}
          },
          error: 'Real API call failed, showing demo data',
          error_details: error.response?.data || error.message
        }
      ]);
    }
  }
});

// Ads endpoint
app.get('/api/v1/facebook/ads/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('Fetching ads for ad account:', adAccountId);

    // Ensure ad account ID has proper format
    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('Using formatted ad account ID:', formattedAdAccountId);

    // Make real API call to Facebook to get ads with enhanced creative fields
    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/ads`, {
        params: {
          fields: 'id,name,adset_id,campaign_id,status,created_time,updated_time,creative{id,name,title,body,image_url,video_id,thumbnail_url,image_hash,object_story_spec{link_data{message,name,description,caption,picture,call_to_action},video_data{message,title,video_id,call_to_action},photo_data{message,url}},call_to_action,link_url,object_id,object_story_id,object_type,object_url},bid_amount,configured_status,effective_status',
          access_token: accessToken,
          limit: 50
        }
      });
    });

    console.log('Facebook ads retrieved:', response.data);

    // Get performance insights for each ad
    const adsWithInsights = await Promise.all(
      (response.data.data || []).map(async (ad) => {
        try {
          const insightsResponse = await axios.get(`https://graph.facebook.com/v23.0/${ad.id}/insights`, {
            params: {
              fields: 'impressions,clicks,spend,cpm,cpc,ctr,reach,frequency,actions,cost_per_action_type,unique_clicks,unique_ctr,cost_per_unique_click,social_spend,inline_link_clicks,inline_link_click_ctr,cost_per_inline_link_click',
              access_token: accessToken,
              date_preset: 'last_30d'
            }
          });

          const insights = insightsResponse.data.data[0] || {};

          // Format creative data for better frontend consumption
          const formattedCreative = formatCreativeData(ad.creative);

          // Log creative data for debugging (first few ads only)
          if ((response.data.data || []).indexOf(ad) < 3) {
            console.log(`Creative data for ad ${ad.id}:`, JSON.stringify(formattedCreative, null, 2));
          }

          return {
            ...ad,
            creative_formatted: formattedCreative,
            insights: {
              impressions: insights.impressions || '0',
              clicks: insights.clicks || '0',
              spend: insights.spend || '0.00',
              cpm: insights.cpm || '0.00',
              cpc: insights.cpc || '0.00',
              ctr: insights.ctr || '0.00',
              reach: insights.reach || '0',
              frequency: insights.frequency || '0.00',
              actions: insights.actions || [],
              cost_per_action_type: insights.cost_per_action_type || [],

              unique_clicks: insights.unique_clicks || '0',
              unique_ctr: insights.unique_ctr || '0.00',
              cost_per_unique_click: insights.cost_per_unique_click || '0.00',
              social_spend: insights.social_spend || '0.00',
              inline_link_clicks: insights.inline_link_clicks || '0',
              inline_link_click_ctr: insights.inline_link_click_ctr || '0.00',
              cost_per_inline_link_click: insights.cost_per_inline_link_click || '0.00'
            }
          };
        } catch (insightsError) {
          console.log(`No insights available for ad ${ad.id}:`, insightsError.response?.data?.error?.message || 'Unknown error');

          // Format creative data even when insights fail
          const formattedCreative = formatCreativeData(ad.creative);

          // Log creative data for debugging (first few ads only)
          if ((response.data.data || []).indexOf(ad) < 3) {
            console.log(`Creative data for ad ${ad.id} (no insights):`, JSON.stringify(formattedCreative, null, 2));
          }

          return {
            ...ad,
            creative_formatted: formattedCreative,
            insights: {
              impressions: '0',
              clicks: '0',
              spend: '0.00',
              cpm: '0.00',
              cpc: '0.00',
              ctr: '0.00',
              reach: '0',
              frequency: '0.00',
              actions: [],
              cost_per_action_type: [],

              unique_clicks: '0',
              unique_ctr: '0.00',
              cost_per_unique_click: '0.00',
              social_spend: '0.00',
              inline_link_clicks: '0',
              inline_link_click_ctr: '0.00',
              cost_per_inline_link_click: '0.00'
            }
          };
        }
      })
    );

    res.json(adsWithInsights);
  } catch (error) {
    console.error('Facebook ads retrieval error:', error.response?.data || error.message);

    // Fallback to demo data if API fails
    res.json([
      {
        id: 'demo_ad_1',
        name: 'Demo Ad (API Error)',
        adset_id: 'demo_adset_1',
        campaign_id: 'demo_campaign_1',
        status: 'PAUSED',
        created_time: '2024-01-15T10:00:00Z',
        error: 'Real API call failed, showing demo data',
        error_details: error.response?.data || error.message
      }
    ]);
  }
});

// Lead forms endpoint
app.get('/api/v1/facebook/leadforms/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('Fetching lead forms for ad account:', adAccountId);

    // Ensure ad account ID has proper format
    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('Using formatted ad account ID:', formattedAdAccountId);

    // Make real API call to Facebook to get lead forms
    const axios = require('axios');
    const response = await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/leadgen_forms`, {
      params: {
        fields: 'id,name,status,created_time,expired_leads_count,leads_count,locale,page,page_id,privacy_policy_url,questions,thank_you_page,context_card,follow_up_action_url,is_continued_flow,leadgen_export_csv_url,legal_content,organic_lead_retrieval_authorized,page_id,qualifiers,question_page_custom_headline,tracking_parameters',
        access_token: accessToken,
        limit: 50
      }
    });

    console.log('Facebook lead forms retrieved:', response.data);

    res.json(response.data.data || []);
  } catch (error) {
    console.error('Facebook lead forms retrieval error:', error.response?.data || error.message);

    // Fallback to empty array if API fails
    res.json([]);
  }
});

// Custom audiences endpoint
app.get('/api/v1/facebook/audiences/:adAccountId', async (req, res) => {
  try {
    const { adAccountId } = req.params;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('Fetching custom audiences for ad account:', adAccountId);

    // Ensure ad account ID has proper format
    const formattedAdAccountId = ensureActPrefix(adAccountId);
    console.log('Using formatted ad account ID:', formattedAdAccountId);

    // Make real API call to Facebook to get custom audiences
    const axios = require('axios');
    const response = await axios.get(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/customaudiences`, {
      params: {
        fields: 'id,name,description,approximate_count,customer_file_source,data_source,delivery_status,external_event_source,is_value_based,lookalike_audience_ids,lookalike_spec,operation_status,opt_out_link,permission_for_actions,pixel_id,retention_days,rule,rule_aggregation,rule_v2,seed_audience,sharing_status,subtype,time_content_updated,time_created,time_updated',
        access_token: accessToken,
        limit: 50
      }
    });

    console.log('Facebook custom audiences retrieved:', response.data);

    res.json(response.data.data || []);
  } catch (error) {
    console.error('Facebook custom audiences retrieval error:', error.response?.data || error.message);

    // Fallback to empty array if API fails
    res.json([]);
  }
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message
  });
});

// Create Ad Set endpoint
app.post('/api/v1/facebook/adsets', async (req, res) => {
  try {
    const {
      campaignId,
      name,
      targeting,
      dailyBudget,
      lifetimeBudget,
      startTime,
      endTime,
      bidStrategy,
      bidAmount,
      optimizationGoal,
      billingEvent,
      status = 'PAUSED'
    } = req.body;

    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    if (!campaignId || !name || !targeting) {
      return res.status(400).json({
        error: 'Missing required fields: campaignId, name, and targeting are required'
      });
    }

    console.log('🚀 Creating Facebook ad set:', {
      campaignId,
      name,
      targeting: JSON.stringify(targeting).substring(0, 100) + '...',
      dailyBudget,
      lifetimeBudget,
      status
    });

    // Prepare ad set data
    const adSetData = {
      name: name,
      campaign_id: campaignId,
      targeting: targeting,
      status: status,
      optimization_goal: optimizationGoal || 'LINK_CLICKS',
      billing_event: billingEvent || 'LINK_CLICKS',
      bid_strategy: bidStrategy || 'LOWEST_COST_WITHOUT_CAP'
    };

    // Add budget (either daily or lifetime, not both)
    if (dailyBudget) {
      adSetData.daily_budget = parseInt(dailyBudget) * 100; // Convert to cents
    } else if (lifetimeBudget) {
      adSetData.lifetime_budget = parseInt(lifetimeBudget) * 100; // Convert to cents
    }

    // Add schedule if provided
    if (startTime) {
      adSetData.start_time = startTime;
    }
    if (endTime) {
      adSetData.end_time = endTime;
    }

    // Add bid amount if provided
    if (bidAmount) {
      adSetData.bid_amount = parseInt(bidAmount) * 100; // Convert to cents
    }

    // Make rate-limited API call to Facebook to create ad set
    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.post(`https://graph.facebook.com/v23.0/${campaignId}/adsets`, null, {
        params: {
          ...adSetData,
          access_token: accessToken
        }
      });
    });

    console.log('✅ Facebook ad set created successfully:', response.data);

    // Note: Cache clearing should be handled by the calling function that knows the ad account ID

    res.json({
      success: true,
      adSet: response.data,
      message: 'Ad set created successfully'
    });

  } catch (error) {
    console.error('Facebook ad set creation error:', error.response?.data || error.message);
    res.status(500).json({
      error: 'Failed to create ad set',
      details: error.response?.data || error.message
    });
  }
});

// Create Ad endpoint
app.post('/api/v1/facebook/ads', async (req, res) => {
  try {
    const {
      adSetId,
      name,
      creative,
      status = 'PAUSED'
    } = req.body;

    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    if (!adSetId || !name || !creative) {
      return res.status(400).json({
        error: 'Missing required fields: adSetId, name, and creative are required'
      });
    }

    console.log('🚀 Creating Facebook ad:', {
      adSetId,
      name,
      creative: JSON.stringify(creative).substring(0, 100) + '...',
      status
    });

    // Make rate-limited API call to Facebook to create ad
    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.post(`https://graph.facebook.com/v23.0/${adSetId}/ads`, null, {
        params: {
          name: name,
          adset_id: adSetId,
          creative: JSON.stringify(creative),
          status: status,
          access_token: accessToken
        }
      });
    });

    console.log('✅ Facebook ad created successfully:', response.data);

    // Note: Cache clearing should be handled by the calling function that knows the ad account ID

    res.json({
      success: true,
      ad: response.data,
      message: 'Ad created successfully'
    });

  } catch (error) {
    console.error('Facebook ad creation error:', error.response?.data || error.message);
    res.status(500).json({
      error: 'Failed to create ad',
      details: error.response?.data || error.message
    });
  }
});

// Create complete campaign hierarchy (Campaign + Ad Set + Ad) endpoint
app.post('/api/v1/facebook/campaign-hierarchy', async (req, res) => {
  try {
    const {
      adAccountId,
      campaign,
      adSet,
      ad
    } = req.body;

    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    if (!adAccountId || !campaign || !adSet || !ad) {
      return res.status(400).json({
        error: 'Missing required fields: adAccountId, campaign, adSet, and ad are required'
      });
    }

    console.log('🚀 Creating complete Facebook campaign hierarchy:', {
      adAccountId,
      campaignName: campaign.name,
      adSetName: adSet.name,
      adName: ad.name
    });

    const formattedAdAccountId = adAccountId.startsWith('act_') ? adAccountId : `act_${adAccountId}`;
    const axios = require('axios');
    const createdEntities = {
      campaign: null,
      adSet: null,
      ad: null
    };

    try {
      // Step 1: Create Campaign
      console.log('📝 Step 1: Creating campaign...');
      const campaignResponse = await rateLimitedApiCall(async () => {
        return await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/campaigns`, null, {
          params: {
            name: campaign.name,
            objective: campaign.objective || 'OUTCOME_TRAFFIC', // Use TRAFFIC objective for LINK_CLICKS optimization
            status: campaign.status || 'PAUSED',
            special_ad_categories: campaign.specialAdCategories || '[]',
            access_token: accessToken
          }
        });
      });

      createdEntities.campaign = campaignResponse.data;
      console.log('✅ Campaign created:', campaignResponse.data.id);

      // Wait for Facebook to process the campaign creation
      console.log('⏳ Waiting for Facebook to process campaign creation...');
      await new Promise(resolve => setTimeout(resolve, 5000)); // 5 second delay

      // Step 2: Create Ad Set
      console.log('📝 Step 2: Creating ad set...');
      const adSetData = {
        name: adSet.name,
        campaign_id: campaignResponse.data.id,
        targeting: JSON.stringify(adSet.targeting || {
          geo_locations: { countries: ['US'] },
          age_min: 18,
          age_max: 65
        }),
        status: adSet.status || 'PAUSED',
        optimization_goal: 'LINK_CLICKS', // Keep LINK_CLICKS optimization
        billing_event: 'IMPRESSIONS', // Force IMPRESSIONS billing for account restrictions
        bid_strategy: 'LOWEST_COST_WITHOUT_CAP' // "Highest Volume" strategy (updated API format)
      };

      console.log('📊 Ad Set Data:', JSON.stringify(adSetData, null, 2));

      // Add budget (required by Facebook)
      if (adSet.dailyBudget) {
        adSetData.daily_budget = parseInt(adSet.dailyBudget) * 100;
      } else if (adSet.lifetimeBudget) {
        adSetData.lifetime_budget = parseInt(adSet.lifetimeBudget) * 100;
      } else {
        // Default daily budget required by Facebook
        adSetData.daily_budget = 500; // $5.00 in cents (minimum for new accounts)
        console.log('💰 Using default daily budget: $5.00');
      }

      // Add schedule
      if (adSet.startTime) adSetData.start_time = adSet.startTime;
      if (adSet.endTime) adSetData.end_time = adSet.endTime;

      // Set bid amount only for strategies that require it
      if (adSetData.bid_strategy === 'LOWEST_COST_WITH_BID_CAP' ||
          adSetData.bid_strategy === 'COST_CAP' ||
          adSetData.bid_strategy === 'LOWEST_COST_WITH_MIN_ROAS') {
        if (adSet.bidAmount) {
          adSetData.bid_amount = parseInt(adSet.bidAmount) * 100;
        } else {
          adSetData.bid_amount = 200; // 2 cents minimum for bid cap strategies
          console.log('💰 Using minimum bid amount: $0.02 for bid cap strategy');
        }
      } else {
        console.log('💰 No bid amount set - using automated bidding for LOWEST_COST_WITHOUT_CAP');
      }

      const adSetResponse = await rateLimitedApiCall(async () => {
        return await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/adsets`, null, {
          params: {
            ...adSetData,
            access_token: accessToken
          }
        });
      });

      createdEntities.adSet = adSetResponse.data;
      console.log('✅ Ad Set created:', adSetResponse.data.id);

      // Wait for Facebook to process the ad set creation
      console.log('⏳ Waiting for Facebook to process ad set creation...');
      await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay

      // Step 3: Create Ad
      console.log('📝 Step 3: Creating ad...');

      // Force reliable URL to prevent image download issues
      const developmentCreative = {
        object_story_spec: {
          page_id: '***************', // Always use Pressure Max page
          link_data: {
            link: 'https://facebook.com', // Force reliable URL
            message: ad.creative.object_story_spec?.link_data?.message || 'Test ad message',
            name: ad.creative.object_story_spec?.link_data?.name || 'Test Ad',
            description: ad.creative.object_story_spec?.link_data?.description || 'Test description',
            call_to_action: {
              type: 'LEARN_MORE'
            }
          }
        }
      };

      console.log('📊 Development Creative:', JSON.stringify(developmentCreative, null, 2));

      // Create ad with development mode compatibility
      const adResponse = await rateLimitedApiCall(async () => {
        return await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/ads`, null, {
          params: {
            name: ad.name,
            adset_id: adSetResponse.data.id,
            creative: JSON.stringify(developmentCreative),
            status: 'PAUSED', // Always start paused for development mode
            access_token: accessToken
          }
        });
      });

      createdEntities.ad = adResponse.data;
      console.log('✅ Ad created:', adResponse.data.id);

      // Clear cache to force refresh
      clearAdAccountCache(formattedAdAccountId);

      res.json({
        success: true,
        entities: createdEntities,
        message: 'Complete campaign hierarchy created successfully',
        summary: {
          campaignId: createdEntities.campaign.id,
          adSetId: createdEntities.adSet.id,
          adId: createdEntities.ad.id
        }
      });

    } catch (error) {
      // Rollback logic - delete created entities if something fails
      console.error('❌ Error in campaign hierarchy creation, attempting rollback...');

      const rollbackPromises = [];

      if (createdEntities.ad) {
        rollbackPromises.push(
          axios.delete(`https://graph.facebook.com/v23.0/${createdEntities.ad.id}`, {
            params: { access_token: accessToken }
          }).catch(e => console.log('Rollback ad failed:', e.message))
        );
      }

      if (createdEntities.adSet) {
        rollbackPromises.push(
          axios.delete(`https://graph.facebook.com/v23.0/${createdEntities.adSet.id}`, {
            params: { access_token: accessToken }
          }).catch(e => console.log('Rollback ad set failed:', e.message))
        );
      }

      if (createdEntities.campaign) {
        rollbackPromises.push(
          axios.delete(`https://graph.facebook.com/v23.0/${createdEntities.campaign.id}`, {
            params: { access_token: accessToken }
          }).catch(e => console.log('Rollback campaign failed:', e.message))
        );
      }

      await Promise.all(rollbackPromises);
      console.log('🔄 Rollback completed');

      throw error; // Re-throw to be caught by outer catch
    }

  } catch (error) {
    console.error('Facebook campaign hierarchy creation error:', error.response?.data || error.message);
    res.status(500).json({
      error: 'Failed to create campaign hierarchy',
      details: error.response?.data || error.message
    });
  }
});

// Get targeting options endpoint
app.get('/api/v1/facebook/targeting-options', (req, res) => {
  const targetingOptions = {
    ageRanges: [
      { min: 18, max: 24, label: '18-24' },
      { min: 25, max: 34, label: '25-34' },
      { min: 35, max: 44, label: '35-44' },
      { min: 45, max: 54, label: '45-54' },
      { min: 55, max: 64, label: '55-64' },
      { min: 65, max: null, label: '65+' }
    ],
    genders: [
      { value: 1, label: 'Male' },
      { value: 2, label: 'Female' },
      { value: 0, label: 'All' }
    ],
    placements: [
      { value: 'facebook_feeds', label: 'Facebook Feed' },
      { value: 'instagram_feed', label: 'Instagram Feed' },
      { value: 'instagram_stories', label: 'Instagram Stories' },
      { value: 'facebook_stories', label: 'Facebook Stories' },
      { value: 'facebook_right_hand_column', label: 'Facebook Right Column' },
      { value: 'facebook_marketplace', label: 'Facebook Marketplace' },
      { value: 'facebook_video_feeds', label: 'Facebook Video Feeds' },
      { value: 'facebook_reels', label: 'Facebook Reels' },
      { value: 'instagram_reels', label: 'Instagram Reels' },
      { value: 'audience_network_native', label: 'Audience Network Native' },
      { value: 'audience_network_banner', label: 'Audience Network Banner' },
      { value: 'audience_network_interstitial', label: 'Audience Network Interstitial' },
      { value: 'audience_network_rewarded_video', label: 'Audience Network Rewarded Video' },
      { value: 'messenger_inbox', label: 'Messenger Inbox' },
      { value: 'messenger_stories', label: 'Messenger Stories' }
    ],
    optimizationGoals: [
      { value: 'LINK_CLICKS', label: 'Link Clicks' },
      { value: 'IMPRESSIONS', label: 'Impressions' },
      { value: 'REACH', label: 'Reach' },
      { value: 'LANDING_PAGE_VIEWS', label: 'Landing Page Views' },
      { value: 'POST_ENGAGEMENT', label: 'Post Engagement' },
      { value: 'PAGE_LIKES', label: 'Page Likes' },
      { value: 'LEAD_GENERATION', label: 'Lead Generation' },
      { value: 'CONVERSIONS', label: 'Conversions' },
      { value: 'APP_INSTALLS', label: 'App Installs' },
      { value: 'VIDEO_VIEWS', label: 'Video Views' },
      { value: 'THRUPLAY', label: 'ThruPlay' }
    ],
    billingEvents: [
      { value: 'IMPRESSIONS', label: 'Impressions' },
      { value: 'LINK_CLICKS', label: 'Link Clicks' },
      { value: 'POST_ENGAGEMENT', label: 'Post Engagement' },
      { value: 'PAGE_LIKES', label: 'Page Likes' },
      { value: 'APP_INSTALLS', label: 'App Installs' },
      { value: 'VIDEO_VIEWS', label: 'Video Views' },
      { value: 'THRUPLAY', label: 'ThruPlay' }
    ],
    bidStrategies: [
      { value: 'LOWEST_COST_WITHOUT_CAP', label: 'Lowest Cost' },
      { value: 'LOWEST_COST_WITH_BID_CAP', label: 'Lowest Cost with Bid Cap' },
      { value: 'TARGET_COST', label: 'Target Cost' }
    ]
  };

  res.json(targetingOptions);
});

// Get creative templates endpoint
app.get('/api/v1/facebook/creative-templates', (req, res) => {
  const creativeTemplates = {
    adFormats: [
      { value: 'SINGLE_IMAGE', label: 'Single Image' },
      { value: 'SINGLE_VIDEO', label: 'Single Video' },
      { value: 'CAROUSEL', label: 'Carousel' },
      { value: 'COLLECTION', label: 'Collection' },
      { value: 'SLIDESHOW', label: 'Slideshow' }
    ],
    callToActionTypes: [
      { value: 'LEARN_MORE', label: 'Learn More' },
      { value: 'SHOP_NOW', label: 'Shop Now' },
      { value: 'BOOK_TRAVEL', label: 'Book Travel' },
      { value: 'DOWNLOAD', label: 'Download' },
      { value: 'GET_QUOTE', label: 'Get Quote' },
      { value: 'CONTACT_US', label: 'Contact Us' },
      { value: 'APPLY_NOW', label: 'Apply Now' },
      { value: 'SIGN_UP', label: 'Sign Up' },
      { value: 'WATCH_MORE', label: 'Watch More' },
      { value: 'PLAY_GAME', label: 'Play Game' },
      { value: 'INSTALL_APP', label: 'Install App' },
      { value: 'USE_APP', label: 'Use App' },
      { value: 'INSTALL_MOBILE_APP', label: 'Install Mobile App' },
      { value: 'USE_MOBILE_APP', label: 'Use Mobile App' },
      { value: 'SUBSCRIBE', label: 'Subscribe' },
      { value: 'NO_BUTTON', label: 'No Button' }
    ],
    sampleCreatives: {
      SINGLE_IMAGE: {
        object_story_spec: {
          page_id: "YOUR_PAGE_ID",
          link_data: {
            image_hash: "IMAGE_HASH",
            link: "https://example.com",
            message: "Check out our amazing product!",
            name: "Product Name",
            description: "Product description goes here",
            call_to_action: {
              type: "LEARN_MORE",
              value: {
                link: "https://example.com"
              }
            }
          }
        }
      },
      SINGLE_VIDEO: {
        object_story_spec: {
          page_id: "YOUR_PAGE_ID",
          video_data: {
            video_id: "VIDEO_ID",
            message: "Watch our amazing video!",
            call_to_action: {
              type: "WATCH_MORE",
              value: {
                link: "https://example.com"
              }
            }
          }
        }
      }
    }
  };

  res.json(creativeTemplates);
});

// Get lead form templates endpoint
app.get('/api/v1/facebook/leadform-templates', (req, res) => {
  const leadFormTemplates = {
    questionTypes: [
      { value: 'FIRST_NAME', label: 'First Name', required: true },
      { value: 'LAST_NAME', label: 'Last Name', required: true },
      { value: 'FULL_NAME', label: 'Full Name', required: true },
      { value: 'EMAIL', label: 'Email Address', required: true },
      { value: 'PHONE', label: 'Phone Number', required: false },
      { value: 'ZIP', label: 'ZIP Code', required: false },
      { value: 'CITY', label: 'City', required: false },
      { value: 'STATE', label: 'State', required: false },
      { value: 'COUNTRY', label: 'Country', required: false },
      { value: 'GENDER', label: 'Gender', required: false },
      { value: 'DATE_OF_BIRTH', label: 'Date of Birth', required: false },
      { value: 'RELATIONSHIP_STATUS', label: 'Relationship Status', required: false },
      { value: 'WORK_EMAIL', label: 'Work Email', required: false },
      { value: 'COMPANY_NAME', label: 'Company Name', required: false },
      { value: 'JOB_TITLE', label: 'Job Title', required: false },
      { value: 'WORK_PHONE_NUMBER', label: 'Work Phone Number', required: false },
      { value: 'CUSTOM', label: 'Custom Question', required: false }
    ],
    templates: [
      {
        name: 'Basic Contact Form',
        description: 'Simple contact form with essential information',
        questions: [
          { type: 'FIRST_NAME', key: 'first_name' },
          { type: 'LAST_NAME', key: 'last_name' },
          { type: 'EMAIL', key: 'email' },
          { type: 'PHONE', key: 'phone' }
        ]
      },
      {
        name: 'Business Lead Form',
        description: 'Professional lead form for B2B campaigns',
        questions: [
          { type: 'FIRST_NAME', key: 'first_name' },
          { type: 'LAST_NAME', key: 'last_name' },
          { type: 'WORK_EMAIL', key: 'work_email' },
          { type: 'COMPANY_NAME', key: 'company_name' },
          { type: 'JOB_TITLE', key: 'job_title' },
          { type: 'WORK_PHONE_NUMBER', key: 'work_phone' }
        ]
      },
      {
        name: 'Service Quote Request',
        description: 'Form for service-based businesses to collect quote requests',
        questions: [
          { type: 'FULL_NAME', key: 'full_name' },
          { type: 'EMAIL', key: 'email' },
          { type: 'PHONE', key: 'phone' },
          { type: 'ZIP', key: 'zip_code' },
          {
            type: 'CUSTOM',
            key: 'service_type',
            question: 'What service are you interested in?',
            options: ['Pressure Washing', 'Window Cleaning', 'Gutter Cleaning', 'Other']
          }
        ]
      },
      {
        name: 'Newsletter Signup',
        description: 'Simple newsletter subscription form',
        questions: [
          { type: 'FIRST_NAME', key: 'first_name' },
          { type: 'EMAIL', key: 'email' }
        ]
      }
    ]
  };

  res.json(leadFormTemplates);
});

// Get Facebook Pages endpoint
app.get('/api/v1/facebook/pages', async (req, res) => {
  try {
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    console.log('🔍 Fetching Facebook pages...');

    const axios = require('axios');
    const response = await rateLimitedApiCall(async () => {
      return await axios.get(`https://graph.facebook.com/v23.0/me/accounts`, {
        params: {
          access_token: accessToken,
          fields: 'id,name,access_token'
        }
      });
    });

    console.log('✅ Facebook pages retrieved:', response.data.data?.length || 0, 'pages');

    res.json(response.data.data || []);
  } catch (error) {
    console.error('Facebook pages error:', error.response?.data || error.message);

    // Return demo page data as fallback
    const demoPages = [
      {
        id: 'demo_page_123',
        name: 'Demo Page',
        access_token: 'demo_token'
      }
    ];

    res.json(demoPages);
  }
});

// Create campaign only (workaround for billing restrictions)
app.post('/api/v1/facebook/campaign-only', async (req, res) => {
  try {
    const { adAccountId, campaign } = req.body;
    const accessToken = 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY';

    if (!adAccountId || !campaign) {
      return res.status(400).json({
        error: 'Missing required fields: adAccountId and campaign are required'
      });
    }

    console.log('🚀 Creating Facebook campaign only:', {
      adAccountId,
      campaignName: campaign.name
    });

    const formattedAdAccountId = adAccountId.startsWith('act_') ? adAccountId : `act_${adAccountId}`;
    const axios = require('axios');

    // Create Campaign
    console.log('📝 Creating campaign...');
    const campaignResponse = await rateLimitedApiCall(async () => {
      return await axios.post(`https://graph.facebook.com/v23.0/${formattedAdAccountId}/campaigns`, null, {
        params: {
          name: campaign.name,
          objective: campaign.objective,
          status: campaign.status || 'PAUSED',
          special_ad_categories: campaign.specialAdCategories || '[]',
          access_token: accessToken
        }
      });
    });

    console.log('✅ Campaign created successfully:', campaignResponse.data.id);

    // Clear cache to force refresh
    clearAdAccountCache(formattedAdAccountId);

    res.json({
      success: true,
      message: 'Campaign created successfully! Ad sets and ads can be added later in Facebook Ads Manager.',
      campaign: campaignResponse.data,
      note: 'Due to Facebook billing restrictions for new accounts, only the campaign was created. You can add ad sets and ads manually in Facebook Ads Manager.'
    });

  } catch (error) {
    console.error('Facebook campaign creation error:', error.response?.data || error.message);
    res.status(500).json({
      error: 'Failed to create campaign',
      details: error.response?.data || error.message
    });
  }
});

// Campaign objectives endpoint
app.get('/api/v1/facebook/campaign-objectives', (req, res) => {
  const objectives = {
    recommended: [
      'OUTCOME_TRAFFIC',
      'OUTCOME_ENGAGEMENT',
      'OUTCOME_LEADS',
      'OUTCOME_SALES',
      'OUTCOME_AWARENESS',
      'OUTCOME_APP_PROMOTION'
    ],
    legacy: [
      'REACH',
      'CONVERSIONS',
      'BRAND_AWARENESS',
      'MESSAGES',
      'VIDEO_VIEWS',
      'STORE_VISITS',
      'APP_INSTALLS',
      'LEAD_GENERATION',
      'PAGE_LIKES'
    ]
  };

  res.json({
    ...objectives,
    message: 'OUTCOME-based objectives are recommended for better performance and future compatibility'
  });
});

// Protected dashboard routes - require authentication
app.get('/', requireAuth, (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/testing', requireAuth, (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'testing.html'));
});

app.get('/facebook-ads', requireAuth, (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'facebook-ads.html'));
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `The requested endpoint ${req.originalUrl} does not exist`
  });
});

app.listen(port, () => {
  console.log(`🚀 Rate-Limited Pressure Max API server running on port ${port}`);
  console.log(`🏠 Dashboard available at http://localhost:${port}/`);
  console.log(`📱 Facebook Ads available at http://localhost:${port}/facebook-ads`);
  console.log(`🧪 Testing interface available at http://localhost:${port}/testing`);
  console.log(`📚 Health check available at http://localhost:${port}/health`);
  console.log(`🔗 CORS enabled for ${process.env.FRONTEND_URL || 'http://localhost:3001'}`);
  console.log(`⏱️ API rate limiting: ${API_DELAY}ms delay between calls`);
  console.log(`💾 Cache duration: ${CACHE_DURATION / 1000}s`);
});

module.exports = app;
